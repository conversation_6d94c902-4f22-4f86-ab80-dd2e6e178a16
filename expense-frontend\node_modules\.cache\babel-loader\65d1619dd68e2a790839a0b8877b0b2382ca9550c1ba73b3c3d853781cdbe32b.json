{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\pages\\\\Settlements.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Layout from '../components/Layout/Layout';\nimport { CreditCard, DollarSign, ArrowRight, CheckCircle, AlertCircle, User, Clock, ExternalLink } from 'lucide-react';\nimport { expensesAPI } from '../services/api';\nimport { formatCurrency, toNumber, safeAbs, formatNumber } from '../utils/formatters';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settlements = () => {\n  _s();\n  const navigate = useNavigate();\n  const [balances, setBalances] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showSettleModal, setShowSettleModal] = useState(false);\n  const [selectedBalance, setSelectedBalance] = useState(null);\n  const [settlementAmount, setSettlementAmount] = useState('');\n  const [processing, setProcessing] = useState(false);\n  const loadBalances = async () => {\n    try {\n      const response = await expensesAPI.getBalances();\n      setBalances(response.data);\n    } catch (error) {\n      toast.error('Failed to load balances');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSettleDebt = async e => {\n    e.preventDefault();\n    if (!selectedBalance || !settlementAmount) return;\n    const amount = parseFloat(settlementAmount);\n    if (amount <= 0) {\n      toast.error('Amount must be greater than 0');\n      return;\n    }\n    const balanceAmount = toNumber(selectedBalance.amount);\n    if (balanceAmount >= 0) {\n      toast.error('You cannot settle with someone who owes you money');\n      return;\n    }\n    const maxAmount = safeAbs(selectedBalance.amount);\n    if (amount > maxAmount) {\n      toast.error(`Amount cannot exceed ${formatCurrency(maxAmount)}`);\n      return;\n    }\n    setProcessing(true);\n    try {\n      const settlementData = {\n        target_user_id: selectedBalance.user_id,\n        amount: amount\n      };\n      await expensesAPI.settleDebt(settlementData);\n\n      // Update the balance in the list\n      setBalances(balances.map(balance => {\n        if (balance.user_id === selectedBalance.user_id) {\n          return {\n            ...balance,\n            amount: toNumber(balance.amount) + amount // Add because balance.amount is negative\n          };\n        }\n        return balance;\n      }).filter(balance => safeAbs(balance.amount) > 0.01)); // Remove settled balances\n\n      setShowSettleModal(false);\n      setSelectedBalance(null);\n      setSettlementAmount('');\n      toast.success('Settlement sent for confirmation! The recipient will need to confirm receipt.');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Failed to process settlement');\n    } finally {\n      setProcessing(false);\n    }\n  };\n  const openSettleModal = balance => {\n    setSelectedBalance(balance);\n    setSettlementAmount(formatNumber(safeAbs(balance.amount)));\n    setShowSettleModal(true);\n  };\n\n  // Separate balances into what you owe and what others owe you\n  const youOwe = balances.filter(b => toNumber(b.amount) < 0);\n  const othersOwe = balances.filter(b => toNumber(b.amount) > 0);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"Settlements\",\n      subtitle: \"Manage debt settlements and payments\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"Settlements\",\n    subtitle: \"Manage debt settlements and payments\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card border-blue-200 bg-blue-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-2 bg-blue-100 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"w-5 h-5 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-blue-900\",\n                  children: \"Settlement Confirmation System\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-blue-700\",\n                  children: \"All settlements now require confirmation from the recipient before balances are updated.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/settlement-confirmations'),\n              className: \"btn-ghost text-blue-600 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                className: \"w-4 h-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), \"View Confirmations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"You Owe\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-red-600\",\n                  children: formatCurrency(safeAbs(youOwe.reduce((sum, b) => sum + toNumber(b.amount), 0)))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mt-1\",\n                  children: [youOwe.length, \" \", youOwe.length === 1 ? 'person' : 'people']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-red-100\",\n                children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-6 h-6 text-red-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Others Owe You\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-green-600\",\n                  children: formatCurrency(othersOwe.reduce((sum, b) => sum + toNumber(b.amount), 0))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mt-1\",\n                  children: [othersOwe.length, \" \", othersOwe.length === 1 ? 'person' : 'people']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-green-100\",\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-6 h-6 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), youOwe.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Debts You Owe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Settle your outstanding debts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: youOwe.map(balance => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-red-100 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(User, {\n                    className: \"w-5 h-5 text-red-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: balance.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Outstanding debt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-red-600\",\n                    children: formatCurrency(safeAbs(balance.amount))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => openSettleModal(balance),\n                  className: \"btn-primary text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n                    className: \"w-4 h-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 25\n                  }, this), \"Settle\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this)]\n            }, balance.user_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), othersOwe.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Money Others Owe You\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Outstanding amounts to be received\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: othersOwe.map(balance => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(User, {\n                    className: \"w-5 h-5 text-green-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: balance.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Owes you money\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg font-bold text-green-600\",\n                  children: formatCurrency(balance.amount)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Pending payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this)]\n            }, balance.user_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), balances.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"All Settled Up!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"You have no outstanding balances. Great job keeping things even!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), showSettleModal && selectedBalance && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Settle Debt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: \"You\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mx-3 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-primary-600\",\n                    children: selectedBalance.email.charAt(0).toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Paying to\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium\",\n                children: selectedBalance.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"Total debt: \", formatCurrency(safeAbs(selectedBalance.amount))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSettleDebt,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"amount\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Settlement Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"amount\",\n                  type: \"number\",\n                  step: \"0.01\",\n                  min: \"0.01\",\n                  max: safeAbs(selectedBalance.amount),\n                  value: settlementAmount,\n                  onChange: e => setSettlementAmount(e.target.value),\n                  className: \"input-field pl-10\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: [\"Maximum: \", formatCurrency(safeAbs(selectedBalance.amount))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowSettleModal(false),\n                className: \"flex-1 btn-secondary\",\n                disabled: processing,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"flex-1 btn-primary disabled:opacity-50\",\n                disabled: processing,\n                children: processing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this), \"Processing...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this) : 'Settle Debt'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(Settlements, \"9NKm//k6tZYvBbnbtLJlb4MT4HQ=\", false, function () {\n  return [useNavigate];\n});\n_c = Settlements;\nexport default Settlements;\nvar _c;\n$RefreshReg$(_c, \"Settlements\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Layout", "CreditCard", "DollarSign", "ArrowRight", "CheckCircle", "AlertCircle", "User", "Clock", "ExternalLink", "expensesAPI", "formatCurrency", "toNumber", "safeAbs", "formatNumber", "toast", "jsxDEV", "_jsxDEV", "Settlements", "_s", "navigate", "balances", "setBalances", "loading", "setLoading", "showSettleModal", "setShowSettleModal", "selectedBalance", "setSelectedBalance", "settlementAmount", "setSettlementAmount", "processing", "setProcessing", "loadBalances", "response", "getBalances", "data", "error", "handleSettleDebt", "e", "preventDefault", "amount", "parseFloat", "balanceAmount", "maxAmount", "settlementData", "target_user_id", "user_id", "settleDebt", "map", "balance", "filter", "success", "_error$response", "_error$response$data", "detail", "openSettleModal", "youOwe", "b", "othersOwe", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "reduce", "sum", "length", "email", "char<PERSON>t", "toUpperCase", "onSubmit", "htmlFor", "id", "type", "step", "min", "max", "value", "onChange", "target", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/pages/Settlements.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Layout from '../components/Layout/Layout';\nimport {\n  CreditCard,\n  DollarSign,\n  ArrowRight,\n  CheckCircle,\n  AlertCircle,\n  User,\n\n  Clock,\n  ExternalLink\n} from 'lucide-react';\nimport { Balance, SettlementRequest } from '../types';\nimport { expensesAPI } from '../services/api';\nimport { formatCurrency, toNumber, safeAbs, formatNumber } from '../utils/formatters';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport toast from 'react-hot-toast';\n\nconst Settlements: React.FC = () => {\n  const navigate = useNavigate();\n  const [balances, setBalances] = useState<Balance[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showSettleModal, setShowSettleModal] = useState(false);\n  const [selectedBalance, setSelectedBalance] = useState<Balance | null>(null);\n  const [settlementAmount, setSettlementAmount] = useState('');\n  const [processing, setProcessing] = useState(false);\n\n  const loadBalances = async () => {\n    try {\n      const response = await expensesAPI.getBalances();\n      setBalances(response.data);\n    } catch (error) {\n      toast.error('Failed to load balances');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSettleDebt = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!selectedBalance || !settlementAmount) return;\n\n    const amount = parseFloat(settlementAmount);\n    if (amount <= 0) {\n      toast.error('Amount must be greater than 0');\n      return;\n    }\n\n    const balanceAmount = toNumber(selectedBalance.amount);\n    if (balanceAmount >= 0) {\n      toast.error('You cannot settle with someone who owes you money');\n      return;\n    }\n\n    const maxAmount = safeAbs(selectedBalance.amount);\n    if (amount > maxAmount) {\n      toast.error(`Amount cannot exceed ${formatCurrency(maxAmount)}`);\n      return;\n    }\n\n    setProcessing(true);\n    try {\n      const settlementData: SettlementRequest = {\n        target_user_id: selectedBalance.user_id,\n        amount: amount\n      };\n\n      await expensesAPI.settleDebt(settlementData);\n      \n      // Update the balance in the list\n      setBalances(balances.map(balance => {\n        if (balance.user_id === selectedBalance.user_id) {\n          return {\n            ...balance,\n            amount: toNumber(balance.amount) + amount // Add because balance.amount is negative\n          };\n        }\n        return balance;\n      }).filter(balance => safeAbs(balance.amount) > 0.01)); // Remove settled balances\n\n      setShowSettleModal(false);\n      setSelectedBalance(null);\n      setSettlementAmount('');\n      toast.success('Settlement sent for confirmation! The recipient will need to confirm receipt.');\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to process settlement');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  const openSettleModal = (balance: Balance) => {\n    setSelectedBalance(balance);\n    setSettlementAmount(formatNumber(safeAbs(balance.amount)));\n    setShowSettleModal(true);\n  };\n\n  // Separate balances into what you owe and what others owe you\n  const youOwe = balances.filter(b => toNumber(b.amount) < 0);\n  const othersOwe = balances.filter(b => toNumber(b.amount) > 0);\n\n  if (loading) {\n    return (\n      <Layout title=\"Settlements\" subtitle=\"Manage debt settlements and payments\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout title=\"Settlements\" subtitle=\"Manage debt settlements and payments\">\n      <div className=\"space-y-6\">\n        {/* Settlement Confirmation Notice */}\n        <div className=\"card border-blue-200 bg-blue-50\">\n          <div className=\"card-content\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Clock className=\"w-5 h-5 text-blue-600\" />\n                </div>\n                <div>\n                  <h3 className=\"text-sm font-medium text-blue-900\">Settlement Confirmation System</h3>\n                  <p className=\"text-sm text-blue-700\">\n                    All settlements now require confirmation from the recipient before balances are updated.\n                  </p>\n                </div>\n              </div>\n              <button\n                onClick={() => navigate('/settlement-confirmations')}\n                className=\"btn-ghost text-blue-600 text-sm\"\n              >\n                <ExternalLink className=\"w-4 h-4 mr-1\" />\n                View Confirmations\n              </button>\n            </div>\n          </div>\n        </div>\n        {/* Summary Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">You Owe</p>\n                  <p className=\"text-2xl font-bold text-red-600\">\n                    {formatCurrency(safeAbs(youOwe.reduce((sum, b) => sum + toNumber(b.amount), 0)))}\n                  </p>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    {youOwe.length} {youOwe.length === 1 ? 'person' : 'people'}\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-red-100\">\n                  <AlertCircle className=\"w-6 h-6 text-red-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Others Owe You</p>\n                  <p className=\"text-2xl font-bold text-green-600\">\n                    {formatCurrency(othersOwe.reduce((sum, b) => sum + toNumber(b.amount), 0))}\n                  </p>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    {othersOwe.length} {othersOwe.length === 1 ? 'person' : 'people'}\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-green-100\">\n                  <CheckCircle className=\"w-6 h-6 text-green-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Debts You Owe */}\n        {youOwe.length > 0 && (\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Debts You Owe</h3>\n              <p className=\"text-sm text-gray-500\">Settle your outstanding debts</p>\n            </div>\n            <div className=\"card-content\">\n              <div className=\"space-y-3\">\n                {youOwe.map((balance) => (\n                  <div key={balance.user_id} className=\"flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-10 h-10 bg-red-100 rounded-full flex items-center justify-center\">\n                        <User className=\"w-5 h-5 text-red-600\" />\n                      </div>\n                      <div className=\"ml-3\">\n                        <p className=\"text-sm font-medium text-gray-900\">{balance.email}</p>\n                        <p className=\"text-xs text-gray-500\">Outstanding debt</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"text-right\">\n                        <p className=\"text-lg font-bold text-red-600\">\n                          {formatCurrency(safeAbs(balance.amount))}\n                        </p>\n                      </div>\n                      <button\n                        onClick={() => openSettleModal(balance)}\n                        className=\"btn-primary text-sm\"\n                      >\n                        <CreditCard className=\"w-4 h-4 mr-1\" />\n                        Settle\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Money Others Owe You */}\n        {othersOwe.length > 0 && (\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Money Others Owe You</h3>\n              <p className=\"text-sm text-gray-500\">Outstanding amounts to be received</p>\n            </div>\n            <div className=\"card-content\">\n              <div className=\"space-y-3\">\n                {othersOwe.map((balance) => (\n                  <div key={balance.user_id} className=\"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                        <User className=\"w-5 h-5 text-green-600\" />\n                      </div>\n                      <div className=\"ml-3\">\n                        <p className=\"text-sm font-medium text-gray-900\">{balance.email}</p>\n                        <p className=\"text-xs text-gray-500\">Owes you money</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-lg font-bold text-green-600\">\n                        {formatCurrency(balance.amount)}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">Pending payment</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* No Balances State */}\n        {balances.length === 0 && (\n          <div className=\"text-center py-12\">\n            <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">All Settled Up!</h3>\n            <p className=\"text-gray-500\">\n              You have no outstanding balances. Great job keeping things even!\n            </p>\n          </div>\n        )}\n\n        {/* Settlement Modal */}\n        {showSettleModal && selectedBalance && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Settle Debt</h3>\n              \n              <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\">\n                      <span className=\"text-sm font-medium\">You</span>\n                    </div>\n                    <ArrowRight className=\"w-4 h-4 mx-3 text-gray-400\" />\n                    <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                      <span className=\"text-sm font-medium text-primary-600\">\n                        {selectedBalance.email.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"mt-3 text-center\">\n                  <p className=\"text-sm text-gray-600\">Paying to</p>\n                  <p className=\"font-medium\">{selectedBalance.email}</p>\n                  <p className=\"text-sm text-gray-500\">\n                    Total debt: {formatCurrency(safeAbs(selectedBalance.amount))}\n                  </p>\n                </div>\n              </div>\n\n              <form onSubmit={handleSettleDebt}>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Settlement Amount\n                  </label>\n                  <div className=\"relative\">\n                    <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n                    <input\n                      id=\"amount\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0.01\"\n                      max={safeAbs(selectedBalance.amount)}\n                      value={settlementAmount}\n                      onChange={(e) => setSettlementAmount(e.target.value)}\n                      className=\"input-field pl-10\"\n                      required\n                    />\n                  </div>\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    Maximum: {formatCurrency(safeAbs(selectedBalance.amount))}\n                  </p>\n                </div>\n                \n                <div className=\"flex gap-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowSettleModal(false)}\n                    className=\"flex-1 btn-secondary\"\n                    disabled={processing}\n                  >\n                    Cancel\n                  </button>\n                  <button \n                    type=\"submit\" \n                    className=\"flex-1 btn-primary disabled:opacity-50\"\n                    disabled={processing}\n                  >\n                    {processing ? (\n                      <div className=\"flex items-center justify-center\">\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                        Processing...\n                      </div>\n                    ) : (\n                      'Settle Debt'\n                    )}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Settlements;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SACEC,UAAU,EACVC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,IAAI,EAEJC,KAAK,EACLC,YAAY,QACP,cAAc;AAErB,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,cAAc,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,QAAQ,qBAAqB;AAErF,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMxB,WAAW,CAACyB,WAAW,CAAC,CAAC;MAChDb,WAAW,CAACY,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdtB,KAAK,CAACsB,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAG,MAAOC,CAAkB,IAAK;IACrDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACb,eAAe,IAAI,CAACE,gBAAgB,EAAE;IAE3C,MAAMY,MAAM,GAAGC,UAAU,CAACb,gBAAgB,CAAC;IAC3C,IAAIY,MAAM,IAAI,CAAC,EAAE;MACf1B,KAAK,CAACsB,KAAK,CAAC,+BAA+B,CAAC;MAC5C;IACF;IAEA,MAAMM,aAAa,GAAG/B,QAAQ,CAACe,eAAe,CAACc,MAAM,CAAC;IACtD,IAAIE,aAAa,IAAI,CAAC,EAAE;MACtB5B,KAAK,CAACsB,KAAK,CAAC,mDAAmD,CAAC;MAChE;IACF;IAEA,MAAMO,SAAS,GAAG/B,OAAO,CAACc,eAAe,CAACc,MAAM,CAAC;IACjD,IAAIA,MAAM,GAAGG,SAAS,EAAE;MACtB7B,KAAK,CAACsB,KAAK,CAAC,wBAAwB1B,cAAc,CAACiC,SAAS,CAAC,EAAE,CAAC;MAChE;IACF;IAEAZ,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMa,cAAiC,GAAG;QACxCC,cAAc,EAAEnB,eAAe,CAACoB,OAAO;QACvCN,MAAM,EAAEA;MACV,CAAC;MAED,MAAM/B,WAAW,CAACsC,UAAU,CAACH,cAAc,CAAC;;MAE5C;MACAvB,WAAW,CAACD,QAAQ,CAAC4B,GAAG,CAACC,OAAO,IAAI;QAClC,IAAIA,OAAO,CAACH,OAAO,KAAKpB,eAAe,CAACoB,OAAO,EAAE;UAC/C,OAAO;YACL,GAAGG,OAAO;YACVT,MAAM,EAAE7B,QAAQ,CAACsC,OAAO,CAACT,MAAM,CAAC,GAAGA,MAAM,CAAC;UAC5C,CAAC;QACH;QACA,OAAOS,OAAO;MAChB,CAAC,CAAC,CAACC,MAAM,CAACD,OAAO,IAAIrC,OAAO,CAACqC,OAAO,CAACT,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEvDf,kBAAkB,CAAC,KAAK,CAAC;MACzBE,kBAAkB,CAAC,IAAI,CAAC;MACxBE,mBAAmB,CAAC,EAAE,CAAC;MACvBf,KAAK,CAACqC,OAAO,CAAC,+EAA+E,CAAC;IAChG,CAAC,CAAC,OAAOf,KAAU,EAAE;MAAA,IAAAgB,eAAA,EAAAC,oBAAA;MACnBvC,KAAK,CAACsB,KAAK,CAAC,EAAAgB,eAAA,GAAAhB,KAAK,CAACH,QAAQ,cAAAmB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBjB,IAAI,cAAAkB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,8BAA8B,CAAC;IAC7E,CAAC,SAAS;MACRvB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMwB,eAAe,GAAIN,OAAgB,IAAK;IAC5CtB,kBAAkB,CAACsB,OAAO,CAAC;IAC3BpB,mBAAmB,CAAChB,YAAY,CAACD,OAAO,CAACqC,OAAO,CAACT,MAAM,CAAC,CAAC,CAAC;IAC1Df,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM+B,MAAM,GAAGpC,QAAQ,CAAC8B,MAAM,CAACO,CAAC,IAAI9C,QAAQ,CAAC8C,CAAC,CAACjB,MAAM,CAAC,GAAG,CAAC,CAAC;EAC3D,MAAMkB,SAAS,GAAGtC,QAAQ,CAAC8B,MAAM,CAACO,CAAC,IAAI9C,QAAQ,CAAC8C,CAAC,CAACjB,MAAM,CAAC,GAAG,CAAC,CAAC;EAE9D,IAAIlB,OAAO,EAAE;IACX,oBACEN,OAAA,CAAChB,MAAM;MAAC2D,KAAK,EAAC,aAAa;MAACC,QAAQ,EAAC,sCAAsC;MAAAC,QAAA,eACzE7C,OAAA;QAAK8C,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpD7C,OAAA;UAAK8C,SAAS,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACElD,OAAA,CAAChB,MAAM;IAAC2D,KAAK,EAAC,aAAa;IAACC,QAAQ,EAAC,sCAAsC;IAAAC,QAAA,eACzE7C,OAAA;MAAK8C,SAAS,EAAC,WAAW;MAAAD,QAAA,gBAExB7C,OAAA;QAAK8C,SAAS,EAAC,iCAAiC;QAAAD,QAAA,eAC9C7C,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3B7C,OAAA;YAAK8C,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChD7C,OAAA;cAAK8C,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1C7C,OAAA;gBAAK8C,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,eACzC7C,OAAA,CAACT,KAAK;kBAACuD,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNlD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAI8C,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrFlD,OAAA;kBAAG8C,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,2BAA2B,CAAE;cACrD2C,SAAS,EAAC,iCAAiC;cAAAD,QAAA,gBAE3C7C,OAAA,CAACR,YAAY;gBAACsD,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAK8C,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBACpD7C,OAAA;UAAK8C,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnB7C,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3B7C,OAAA;cAAK8C,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChD7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAG8C,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DlD,OAAA;kBAAG8C,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAC3CnD,cAAc,CAACE,OAAO,CAAC4C,MAAM,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEZ,CAAC,KAAKY,GAAG,GAAG1D,QAAQ,CAAC8C,CAAC,CAACjB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;gBAAC;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACJlD,OAAA;kBAAG8C,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,GACtCL,MAAM,CAACc,MAAM,EAAC,GAAC,EAACd,MAAM,CAACc,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNlD,OAAA;gBAAK8C,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,eACxC7C,OAAA,CAACX,WAAW;kBAACyD,SAAS,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAK8C,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnB7C,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3B7C,OAAA;cAAK8C,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChD7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAG8C,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnElD,OAAA;kBAAG8C,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAC7CnD,cAAc,CAACgD,SAAS,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEZ,CAAC,KAAKY,GAAG,GAAG1D,QAAQ,CAAC8C,CAAC,CAACjB,MAAM,CAAC,EAAE,CAAC,CAAC;gBAAC;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACJlD,OAAA;kBAAG8C,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,GACtCH,SAAS,CAACY,MAAM,EAAC,GAAC,EAACZ,SAAS,CAACY,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNlD,OAAA;gBAAK8C,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eAC1C7C,OAAA,CAACZ,WAAW;kBAAC0D,SAAS,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLV,MAAM,CAACc,MAAM,GAAG,CAAC,iBAChBtD,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnB7C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1B7C,OAAA;YAAI8C,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtElD,OAAA;YAAG8C,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNlD,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3B7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,EACvBL,MAAM,CAACR,GAAG,CAAEC,OAAO,iBAClBjC,OAAA;cAA2B8C,SAAS,EAAC,kFAAkF;cAAAD,QAAA,gBACrH7C,OAAA;gBAAK8C,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC7C,OAAA;kBAAK8C,SAAS,EAAC,oEAAoE;kBAAAD,QAAA,eACjF7C,OAAA,CAACV,IAAI;oBAACwD,SAAS,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNlD,OAAA;kBAAK8C,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBACnB7C,OAAA;oBAAG8C,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAEZ,OAAO,CAACsB;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpElD,OAAA;oBAAG8C,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlD,OAAA;gBAAK8C,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1C7C,OAAA;kBAAK8C,SAAS,EAAC,YAAY;kBAAAD,QAAA,eACzB7C,OAAA;oBAAG8C,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,EAC1CnD,cAAc,CAACE,OAAO,CAACqC,OAAO,CAACT,MAAM,CAAC;kBAAC;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlD,OAAA;kBACEmD,OAAO,EAAEA,CAAA,KAAMZ,eAAe,CAACN,OAAO,CAAE;kBACxCa,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,gBAE/B7C,OAAA,CAACf,UAAU;oBAAC6D,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAvBEjB,OAAO,CAACH,OAAO;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBpB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAR,SAAS,CAACY,MAAM,GAAG,CAAC,iBACnBtD,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnB7C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1B7C,OAAA;YAAI8C,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ElD,OAAA;YAAG8C,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACNlD,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3B7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,EACvBH,SAAS,CAACV,GAAG,CAAEC,OAAO,iBACrBjC,OAAA;cAA2B8C,SAAS,EAAC,sFAAsF;cAAAD,QAAA,gBACzH7C,OAAA;gBAAK8C,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC7C,OAAA;kBAAK8C,SAAS,EAAC,sEAAsE;kBAAAD,QAAA,eACnF7C,OAAA,CAACV,IAAI;oBAACwD,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNlD,OAAA;kBAAK8C,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBACnB7C,OAAA;oBAAG8C,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAEZ,OAAO,CAACsB;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpElD,OAAA;oBAAG8C,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlD,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzB7C,OAAA;kBAAG8C,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAC5CnD,cAAc,CAACuC,OAAO,CAACT,MAAM;gBAAC;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACJlD,OAAA;kBAAG8C,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA,GAfEjB,OAAO,CAACH,OAAO;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBpB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA9C,QAAQ,CAACkD,MAAM,KAAK,CAAC,iBACpBtD,OAAA;QAAK8C,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChC7C,OAAA,CAACZ,WAAW;UAAC0D,SAAS,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjElD,OAAA;UAAI8C,SAAS,EAAC,wCAAwC;UAAAD,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ElD,OAAA;UAAG8C,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAE7B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN,EAGA1C,eAAe,IAAIE,eAAe,iBACjCV,OAAA;QAAK8C,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F7C,OAAA;UAAK8C,SAAS,EAAC,yCAAyC;UAAAD,QAAA,gBACtD7C,OAAA;YAAI8C,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzElD,OAAA;YAAK8C,SAAS,EAAC,gCAAgC;YAAAD,QAAA,gBAC7C7C,OAAA;cAAK8C,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAChD7C,OAAA;gBAAK8C,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC7C,OAAA;kBAAK8C,SAAS,EAAC,mEAAmE;kBAAAD,QAAA,eAChF7C,OAAA;oBAAM8C,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,EAAC;kBAAG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNlD,OAAA,CAACb,UAAU;kBAAC2D,SAAS,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDlD,OAAA;kBAAK8C,SAAS,EAAC,sEAAsE;kBAAAD,QAAA,eACnF7C,OAAA;oBAAM8C,SAAS,EAAC,sCAAsC;oBAAAD,QAAA,EACnDnC,eAAe,CAAC6C,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAAC;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAK8C,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC/B7C,OAAA;gBAAG8C,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClDlD,OAAA;gBAAG8C,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAEnC,eAAe,CAAC6C;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDlD,OAAA;gBAAG8C,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,GAAC,cACvB,EAACnD,cAAc,CAACE,OAAO,CAACc,eAAe,CAACc,MAAM,CAAC,CAAC;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAM0D,QAAQ,EAAErC,gBAAiB;YAAAwB,QAAA,gBAC/B7C,OAAA;cAAK8C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB7C,OAAA;gBAAO2D,OAAO,EAAC,QAAQ;gBAACb,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEjF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlD,OAAA;gBAAK8C,SAAS,EAAC,UAAU;gBAAAD,QAAA,gBACvB7C,OAAA,CAACd,UAAU;kBAAC4D,SAAS,EAAC;gBAA0E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnGlD,OAAA;kBACE4D,EAAE,EAAC,QAAQ;kBACXC,IAAI,EAAC,QAAQ;kBACbC,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,MAAM;kBACVC,GAAG,EAAEpE,OAAO,CAACc,eAAe,CAACc,MAAM,CAAE;kBACrCyC,KAAK,EAAErD,gBAAiB;kBACxBsD,QAAQ,EAAG5C,CAAC,IAAKT,mBAAmB,CAACS,CAAC,CAAC6C,MAAM,CAACF,KAAK,CAAE;kBACrDnB,SAAS,EAAC,mBAAmB;kBAC7BsB,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlD,OAAA;gBAAG8C,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,GAAC,WAC/B,EAACnD,cAAc,CAACE,OAAO,CAACc,eAAe,CAACc,MAAM,CAAC,CAAC;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlD,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB7C,OAAA;gBACE6D,IAAI,EAAC,QAAQ;gBACbV,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC,KAAK,CAAE;gBACzCqC,SAAS,EAAC,sBAAsB;gBAChCuB,QAAQ,EAAEvD,UAAW;gBAAA+B,QAAA,EACtB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlD,OAAA;gBACE6D,IAAI,EAAC,QAAQ;gBACbf,SAAS,EAAC,wCAAwC;gBAClDuB,QAAQ,EAAEvD,UAAW;gBAAA+B,QAAA,EAEpB/B,UAAU,gBACTd,OAAA;kBAAK8C,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,gBAC/C7C,OAAA;oBAAK8C,SAAS,EAAC;kBAAgE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,iBAExF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,GAEN;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAChD,EAAA,CA3UID,WAAqB;EAAA,QACRlB,WAAW;AAAA;AAAAuF,EAAA,GADxBrE,WAAqB;AA6U3B,eAAeA,WAAW;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}