{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { Home, Users, Receipt, CreditCard, MessageSquare, Settings, LogOut, CheckCircle, Clock } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst navigation = [{\n  name: 'Dashboard',\n  href: '/',\n  icon: Home\n}, {\n  name: 'Groups',\n  href: '/groups',\n  icon: Users\n}, {\n  name: 'Expenses',\n  href: '/expenses',\n  icon: Receipt\n}, {\n  name: 'Approvals',\n  href: '/approvals',\n  icon: CheckCircle\n}, {\n  name: 'Settlements',\n  href: '/settlements',\n  icon: CreditCard\n}, {\n  name: 'Confirmations',\n  href: '/settlement-confirmations',\n  icon: Clock\n}, {\n  name: 'AI Assistant',\n  href: '/ai-chat',\n  icon: MessageSquare\n}, {\n  name: 'Settings',\n  href: '/settings',\n  icon: Settings\n}];\nconst Sidebar = () => {\n  _s();\n  var _user$email;\n  const {\n    logout,\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full bg-white border-r border-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center px-6 py-4 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(Receipt, {\n            className: \"w-5 h-5 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-3 text-xl font-semibold text-gray-900\",\n          children: \"ExpenseTracker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex-1 px-4 py-6 space-y-1\",\n      children: navigation.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n        to: item.href,\n        className: ({\n          isActive\n        }) => `sidebar-item ${isActive ? 'sidebar-item-active' : 'sidebar-item-inactive'}`,\n        children: [/*#__PURE__*/_jsxDEV(item.icon, {\n          className: \"w-5 h-5 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), item.name]\n      }, item.name, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 py-4 border-t border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center px-3 py-2 text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-primary-700 font-medium\",\n            children: user === null || user === void 0 ? void 0 : (_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3 flex-1 min-w-0\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-gray-900 truncate\",\n            children: user === null || user === void 0 ? void 0 : user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(NavLink, {\n          to: \"/settings\",\n          className: \"sidebar-item sidebar-item-inactive\",\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            className: \"w-5 h-5 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), \"Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"w-full sidebar-item sidebar-item-inactive text-left\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            className: \"w-5 h-5 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), \"Sign out\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"yz1zAnGXLwdGo6fU0KrJg3494lI=\", false, function () {\n  return [useAuth];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "Home", "Users", "Receipt", "CreditCard", "MessageSquare", "Settings", "LogOut", "CheckCircle", "Clock", "useAuth", "jsxDEV", "_jsxDEV", "navigation", "name", "href", "icon", "Sidebar", "_s", "_user$email", "logout", "user", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "isActive", "email", "char<PERSON>t", "toUpperCase", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport {\n  Home,\n  Users,\n  Receipt,\n  CreditCard,\n  MessageSquare,\n  Settings,\n  LogOut,\n  CheckCircle,\n  Clock,\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: Home },\n  { name: 'Groups', href: '/groups', icon: Users },\n  { name: 'Expenses', href: '/expenses', icon: Receipt },\n  { name: 'Approvals', href: '/approvals', icon: CheckCircle },\n  { name: 'Settlements', href: '/settlements', icon: CreditCard },\n  { name: 'Confirmations', href: '/settlement-confirmations', icon: Clock },\n  { name: 'AI Assistant', href: '/ai-chat', icon: MessageSquare },\n  { name: 'Settings', href: '/settings', icon: Settings },\n];\n\nconst Sidebar: React.FC = () => {\n  const { logout, user } = useAuth();\n\n  return (\n    <div className=\"flex flex-col h-full bg-white border-r border-gray-200\">\n      {/* Logo */}\n      <div className=\"flex items-center px-6 py-4 border-b border-gray-200\">\n        <div className=\"flex items-center\">\n          <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n            <Receipt className=\"w-5 h-5 text-white\" />\n          </div>\n          <span className=\"ml-3 text-xl font-semibold text-gray-900\">\n            ExpenseTracker\n          </span>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-1\">\n        {navigation.map((item) => (\n          <NavLink\n            key={item.name}\n            to={item.href}\n            className={({ isActive }) =>\n              `sidebar-item ${\n                isActive ? 'sidebar-item-active' : 'sidebar-item-inactive'\n              }`\n            }\n          >\n            <item.icon className=\"w-5 h-5 mr-3\" />\n            {item.name}\n          </NavLink>\n        ))}\n      </nav>\n\n      {/* User section */}\n      <div className=\"px-4 py-4 border-t border-gray-200\">\n        <div className=\"flex items-center px-3 py-2 text-sm\">\n          <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\">\n            <span className=\"text-primary-700 font-medium\">\n              {user?.email?.charAt(0).toUpperCase()}\n            </span>\n          </div>\n          <div className=\"ml-3 flex-1 min-w-0\">\n            <p className=\"text-sm font-medium text-gray-900 truncate\">\n              {user?.email}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"mt-2 space-y-1\">\n          <NavLink\n            to=\"/settings\"\n            className=\"sidebar-item sidebar-item-inactive\"\n          >\n            <Settings className=\"w-5 h-5 mr-3\" />\n            Settings\n          </NavLink>\n          \n          <button\n            onClick={logout}\n            className=\"w-full sidebar-item sidebar-item-inactive text-left\"\n          >\n            <LogOut className=\"w-5 h-5 mr-3\" />\n            Sign out\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SACEC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,KAAK,QACA,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,UAAU,GAAG,CACjB;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAEf;AAAK,CAAC,EAC5C;EAAEa,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAEd;AAAM,CAAC,EAChD;EAAEY,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAEb;AAAQ,CAAC,EACtD;EAAEW,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAER;AAAY,CAAC,EAC5D;EAAEM,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAEZ;AAAW,CAAC,EAC/D;EAAEU,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,2BAA2B;EAAEC,IAAI,EAAEP;AAAM,CAAC,EACzE;EAAEK,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAEX;AAAc,CAAC,EAC/D;EAAES,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAEV;AAAS,CAAC,CACxD;AAED,MAAMW,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA;EAC9B,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAElC,oBACEE,OAAA;IAAKU,SAAS,EAAC,wDAAwD;IAAAC,QAAA,gBAErEX,OAAA;MAAKU,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEX,OAAA;QAAKU,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCX,OAAA;UAAKU,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjFX,OAAA,CAACT,OAAO;YAACmB,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNf,OAAA;UAAMU,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNf,OAAA;MAAKU,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EACxCV,UAAU,CAACe,GAAG,CAAEC,IAAI,iBACnBjB,OAAA,CAACZ,OAAO;QAEN8B,EAAE,EAAED,IAAI,CAACd,IAAK;QACdO,SAAS,EAAEA,CAAC;UAAES;QAAS,CAAC,KACtB,gBACEA,QAAQ,GAAG,qBAAqB,GAAG,uBAAuB,EAE7D;QAAAR,QAAA,gBAEDX,OAAA,CAACiB,IAAI,CAACb,IAAI;UAACM,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrCE,IAAI,CAACf,IAAI;MAAA,GATLe,IAAI,CAACf,IAAI;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUP,CACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNf,OAAA;MAAKU,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBACjDX,OAAA;QAAKU,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClDX,OAAA;UAAKU,SAAS,EAAC,sEAAsE;UAAAC,QAAA,eACnFX,OAAA;YAAMU,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAC3CF,IAAI,aAAJA,IAAI,wBAAAF,WAAA,GAAJE,IAAI,CAAEW,KAAK,cAAAb,WAAA,uBAAXA,WAAA,CAAac,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNf,OAAA;UAAKU,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCX,OAAA;YAAGU,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EACtDF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENf,OAAA;QAAKU,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BX,OAAA,CAACZ,OAAO;UACN8B,EAAE,EAAC,WAAW;UACdR,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBAE9CX,OAAA,CAACN,QAAQ;YAACgB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAEVf,OAAA;UACEuB,OAAO,EAAEf,MAAO;UAChBE,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAE/DX,OAAA,CAACL,MAAM;YAACe,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CAtEID,OAAiB;EAAA,QACIP,OAAO;AAAA;AAAA0B,EAAA,GAD5BnB,OAAiB;AAwEvB,eAAeA,OAAO;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}