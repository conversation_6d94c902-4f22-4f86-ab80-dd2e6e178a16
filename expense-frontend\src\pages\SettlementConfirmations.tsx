import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import { 
  CheckCircle, 
  XCircle, 
  Clock,
  DollarSign,
  Calendar,
  User,
  AlertTriangle,
  Check,
  X
} from 'lucide-react';
import { formatCurrency, formatDate } from '../utils/formatters';
import { approvalsAPI } from '../services/api';
import { useAutoRefresh } from '../hooks/useAutoRefresh';
import { notificationService } from '../services/notificationService';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

interface PendingSettlement {
  id: number;
  payer_id: number;
  recipient_id: number;
  amount: number;
  status: string;
  created_at: string;
  confirmed_at?: string;
  description?: string;
  payer: {
    id: number;
    email: string;
  };
  recipient: {
    id: number;
    email: string;
  };
}

const SettlementConfirmations: React.FC = () => {
  const { user } = useAuth();
  const [pendingSettlements, setPendingSettlements] = useState<PendingSettlement[]>([]);
  const [settlementHistory, setSettlementHistory] = useState<PendingSettlement[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState<'pending' | 'history'>('pending');

  const loadData = async () => {
    try {
      const [pendingRes, historyRes] = await Promise.all([
        approvalsAPI.getPendingSettlements(),
        approvalsAPI.getSettlementHistory()
      ]);
      
      setPendingSettlements(pendingRes.data);
      setSettlementHistory(historyRes.data);
    } catch (error) {
      toast.error('Failed to load settlement data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Auto-refresh data when notifications indicate changes
  useAutoRefresh(loadData, []);

  const handleConfirmation = async (settlementId: number, confirmed: boolean) => {
    setProcessing([...processing, settlementId]);
    
    try {
      await approvalsAPI.confirmSettlement(settlementId, confirmed);
      
      // Remove from pending list and add to history
      const settlement = pendingSettlements.find(s => s.id === settlementId);
      if (settlement) {
        setPendingSettlements(pendingSettlements.filter(s => s.id !== settlementId));
        setSettlementHistory([
          { ...settlement, status: confirmed ? 'confirmed' : 'disputed', confirmed_at: new Date().toISOString() },
          ...settlementHistory
        ]);
      }

      // Trigger notification for settlement confirmation
      if (confirmed && settlement) {
        notificationService.notifySettlementConfirmed({
          amount: settlement.amount,
          confirmer: user?.email || 'You'
        });
      }

      toast.success(`Settlement ${confirmed ? 'confirmed' : 'disputed'} successfully!`);
    } catch (error: any) {
      toast.error(error.response?.data?.detail || `Failed to ${confirmed ? 'confirm' : 'dispute'} settlement`);
    } finally {
      setProcessing(processing.filter(id => id !== settlementId));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'text-green-600 bg-green-100';
      case 'disputed': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="w-4 h-4" />;
      case 'disputed': return <XCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <Layout title="Settlement Confirmations" subtitle="Confirm received payments">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Settlement Confirmations" subtitle="Confirm received payments">
      <div className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Confirmations</p>
                  <p className="text-2xl font-bold text-orange-600">{pendingSettlements.length}</p>
                </div>
                <div className="p-3 rounded-lg bg-orange-100">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Pending Amount</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(pendingSettlements.reduce((sum, s) => sum + s.amount, 0))}
                  </p>
                </div>
                <div className="p-3 rounded-lg bg-blue-100">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Settlements</p>
                  <p className="text-2xl font-bold text-primary-600">{settlementHistory.length}</p>
                </div>
                <div className="p-3 rounded-lg bg-primary-100">
                  <CheckCircle className="w-6 h-6 text-primary-600" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('pending')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'pending'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Pending Confirmations ({pendingSettlements.length})
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Settlement History ({settlementHistory.length})
            </button>
          </nav>
        </div>

        {/* Pending Settlements */}
        {activeTab === 'pending' && (
          <div className="space-y-4">
            {pendingSettlements.length === 0 ? (
              <div className="text-center py-12">
                <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Confirmations</h3>
                <p className="text-gray-500">
                  You have no settlement confirmations waiting for your response.
                </p>
              </div>
            ) : (
              pendingSettlements.map((settlement) => (
                <div key={settlement.id} className="card hover:shadow-lg transition-shadow">
                  <div className="card-content">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                          <DollarSign className="w-6 h-6 text-orange-600" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              Payment Received
                            </h3>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(settlement.status)}`}>
                              {getStatusIcon(settlement.status)}
                              <span className="ml-1 capitalize">{settlement.status}</span>
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <User className="w-4 h-4 mr-1" />
                              From: {settlement.payer.email}
                            </div>
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {formatDate(settlement.created_at)}
                            </div>
                            <div className="flex items-center">
                              <DollarSign className="w-4 h-4 mr-1" />
                              Amount: {formatCurrency(settlement.amount)}
                            </div>
                          </div>
                          
                          {settlement.description && (
                            <div className="mt-2 text-sm text-gray-600">
                              <strong>Note:</strong> {settlement.description}
                            </div>
                          )}
                          
                          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm text-blue-800">
                              <strong>Confirm that you received {formatCurrency(settlement.amount)} from {settlement.payer.email}</strong>
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => handleConfirmation(settlement.id, false)}
                          disabled={processing.includes(settlement.id)}
                          className="btn-secondary text-sm disabled:opacity-50"
                        >
                          <X className="w-4 h-4 mr-1" />
                          Dispute
                        </button>
                        <button
                          onClick={() => handleConfirmation(settlement.id, true)}
                          disabled={processing.includes(settlement.id)}
                          className="btn-primary text-sm disabled:opacity-50"
                        >
                          <Check className="w-4 h-4 mr-1" />
                          Confirm
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* Settlement History */}
        {activeTab === 'history' && (
          <div className="space-y-4">
            {settlementHistory.length === 0 ? (
              <div className="text-center py-12">
                <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Settlement History</h3>
                <p className="text-gray-500">
                  Your settlement confirmation history will appear here.
                </p>
              </div>
            ) : (
              settlementHistory.map((settlement) => (
                <div key={settlement.id} className="card">
                  <div className="card-content">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                          settlement.status === 'confirmed' ? 'bg-green-100' : 'bg-red-100'
                        }`}>
                          {getStatusIcon(settlement.status)}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {formatCurrency(settlement.amount)}
                            </h3>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(settlement.status)}`}>
                              <span className="capitalize">{settlement.status}</span>
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div>From: {settlement.payer.email}</div>
                            <div>Date: {formatDate(settlement.created_at)}</div>
                            {settlement.confirmed_at && (
                              <div>Confirmed: {formatDate(settlement.confirmed_at)}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SettlementConfirmations;
