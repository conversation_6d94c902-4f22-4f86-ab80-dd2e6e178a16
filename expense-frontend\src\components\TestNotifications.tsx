/**
 * Test component to verify notifications and auto-refresh are working
 */

import React from 'react';
import { notificationService } from '../services/notificationService';

const TestNotifications: React.FC = () => {
  const handleTestExpenseNotification = () => {
    notificationService.notifyExpenseCreated({
      description: 'Test lunch expense',
      amount: 25.50,
      payer: 'Test User',
      groupName: 'Test Group'
    });
  };

  const handleTestApprovalNotification = () => {
    notificationService.notifyExpenseNeedsApproval({
      description: 'Test dinner expense',
      amount: 45.00,
      payer: 'Test User',
      groupName: 'Test Group'
    });
  };

  const handleTestSettlementNotification = () => {
    notificationService.notifySettlementReceived({
      amount: 30.00,
      payer: 'Test User'
    });
  };

  const handleTestSettlementSent = () => {
    notificationService.notifySettlementSent({
      amount: 50.00,
      recipient: 'Test Recipient'
    });
  };

  const handleTestExpenseApproved = () => {
    notificationService.notifyExpenseApproved({
      description: 'Test approved expense',
      amount: 35.00,
      approver: 'Test Approver'
    });
  };

  const handleTestSettlementConfirmed = () => {
    notificationService.notifySettlementConfirmed({
      amount: 40.00,
      confirmer: 'Test Confirmer'
    });
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Test Real Notifications</h3>
      <div className="grid grid-cols-2 gap-2">
        <button
          onClick={handleTestExpenseNotification}
          className="btn-primary text-sm"
        >
          Test Expense Created
        </button>
        <button
          onClick={handleTestApprovalNotification}
          className="btn-secondary text-sm"
        >
          Test Approval Needed
        </button>
        <button
          onClick={handleTestSettlementNotification}
          className="btn-ghost text-sm"
        >
          Test Settlement Received
        </button>
        <button
          onClick={handleTestSettlementSent}
          className="btn-ghost text-sm"
        >
          Test Settlement Sent
        </button>
        <button
          onClick={handleTestExpenseApproved}
          className="btn-secondary text-sm"
        >
          Test Expense Approved
        </button>
        <button
          onClick={handleTestSettlementConfirmed}
          className="btn-primary text-sm"
        >
          Test Settlement Confirmed
        </button>
      </div>
    </div>
  );
};

export default TestNotifications;
