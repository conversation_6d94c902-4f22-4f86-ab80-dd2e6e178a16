#!/usr/bin/env python3
"""
Test the updated join workflow to ensure Groups page now uses join requests
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'expense-app'))

from app.database import SessionLocal
from app.services.group_management_service import GroupManagementService
from app.models import User, Group, GroupJoinRequest, JoinRequestStatus
from sqlalchemy import and_

def test_join_request_workflow():
    """Test that join requests are properly created and can be processed"""
    db = SessionLocal()
    try:
        print("🔧 Testing Updated Join Request Workflow\n")
        
        # Get test users
        users = db.query(User).limit(3).all()
        if len(users) < 3:
            print("❌ Need at least 3 users for testing")
            return False
            
        group_owner = users[0]
        requester = users[1]
        
        print(f"👑 Group Owner: {group_owner.email}")
        print(f"👤 Requester: {requester.email}")
        
        # Find or create a test group
        test_group = db.query(Group).filter(Group.creator_id == group_owner.id).first()
        if not test_group:
            print("❌ No groups found for testing")
            return False
            
        print(f"🏢 Test Group: {test_group.name} (ID: {test_group.id})")
        
        # Check if requester is already a member
        current_member_ids = [member.id for member in test_group.members]
        if requester.id in current_member_ids:
            print(f"⚠️ {requester.email} is already a member, removing for test...")
            test_group.members = [member for member in test_group.members if member.id != requester.id]
            db.commit()
            
        group_service = GroupManagementService(db)
        
        print("\n=== Step 1: Create Join Request ===")
        
        # Clean up any existing requests for this test
        existing_requests = db.query(GroupJoinRequest).filter(
            and_(
                GroupJoinRequest.group_id == test_group.id,
                GroupJoinRequest.user_id == requester.id
            )
        ).all()
        
        for req in existing_requests:
            db.delete(req)
        db.commit()
        
        # Create new join request (simulating frontend behavior)
        try:
            join_request = group_service.request_to_join_group(
                requester, 
                test_group.id, 
                "I'd like to join this group for testing the updated workflow!"
            )
            print(f"✅ Join request created successfully")
            print(f"   Request ID: {join_request.id}")
            print(f"   Status: {join_request.status}")
            print(f"   Message: {join_request.message}")
            print(f"   Created: {join_request.created_at}")
            
        except ValueError as e:
            print(f"❌ Failed to create join request: {e}")
            return False
            
        print("\n=== Step 2: Verify Request in Owner's Pending List ===")
        
        # Check that the request appears in owner's pending list
        pending_requests = group_service.get_pending_join_requests(group_owner, test_group.id)
        print(f"✅ Found {len(pending_requests)} pending requests for group owner")
        
        request_found = False
        for request in pending_requests:
            if request.id == join_request.id:
                request_found = True
                user = db.query(User).filter(User.id == request.user_id).first()
                print(f"   ✅ Request {request.id}: {user.email} -> {test_group.name}")
                print(f"      Message: '{request.message}'")
                print(f"      Created: {request.created_at}")
                break
                
        if not request_found:
            print(f"❌ Join request not found in owner's pending list")
            return False
            
        print("\n=== Step 3: Approve Join Request ===")
        
        # Approve the request (simulating Group Management Modal behavior)
        try:
            approved_request = group_service.process_join_request(
                group_owner, 
                join_request.id, 
                True
            )
            print(f"✅ Join request approved successfully")
            print(f"   Status: {approved_request.status}")
            print(f"   Processed at: {approved_request.processed_at}")
            print(f"   Processed by: {approved_request.processed_by}")
            
        except ValueError as e:
            print(f"❌ Failed to approve join request: {e}")
            return False
            
        print("\n=== Step 4: Verify User Added to Group ===")
        
        # Check that user was added to group
        db.refresh(test_group)
        updated_member_ids = [member.id for member in test_group.members]
        
        if requester.id in updated_member_ids:
            print(f"✅ User {requester.email} successfully added to group")
            print(f"   Group now has {len(test_group.members)} members:")
            for member in test_group.members:
                role = "👑 Owner" if member.id == test_group.creator_id else "👤 Member"
                print(f"      {role}: {member.email}")
        else:
            print(f"❌ User was not added to group after approval")
            return False
            
        print("\n=== Step 5: Verify No More Pending Requests ===")
        
        # Check that there are no more pending requests for this group
        remaining_requests = group_service.get_pending_join_requests(group_owner, test_group.id)
        print(f"✅ Pending requests after approval: {len(remaining_requests)}")
        
        if len(remaining_requests) == 0:
            print("   ✅ No pending requests remaining (as expected)")
        else:
            print("   ⚠️ Some requests still pending:")
            for req in remaining_requests:
                user = db.query(User).filter(User.id == req.user_id).first()
                print(f"      Request {req.id}: {user.email}")
                
        return True
        
    finally:
        db.close()

def test_duplicate_request_prevention():
    """Test that duplicate requests are prevented"""
    db = SessionLocal()
    try:
        print("\n🔧 Testing Duplicate Request Prevention\n")
        
        users = db.query(User).limit(2).all()
        if len(users) < 2:
            print("❌ Need at least 2 users for testing")
            return False
            
        group_owner = users[0]
        requester = users[1]
        
        test_group = db.query(Group).filter(Group.creator_id == group_owner.id).first()
        if not test_group:
            print("❌ No groups found for testing")
            return False
            
        group_service = GroupManagementService(db)
        
        # Ensure user is not a member
        current_member_ids = [member.id for member in test_group.members]
        if requester.id in current_member_ids:
            test_group.members = [member for member in test_group.members if member.id != requester.id]
            db.commit()
            
        print(f"Testing duplicate prevention for {requester.email} -> {test_group.name}")
        
        # Create first request
        try:
            first_request = group_service.request_to_join_group(
                requester, test_group.id, "First request"
            )
            print(f"✅ First request created: ID {first_request.id}")
        except ValueError as e:
            print(f"❌ First request failed: {e}")
            return False
            
        # Try to create duplicate request
        try:
            duplicate_request = group_service.request_to_join_group(
                requester, test_group.id, "Duplicate request"
            )
            print(f"❌ Duplicate request should have been prevented!")
            return False
        except ValueError as e:
            print(f"✅ Duplicate request correctly prevented: {e}")
            
        return True
        
    finally:
        db.close()

def verify_frontend_changes():
    """Verify that frontend files have been updated correctly"""
    print("\n🔧 Verifying Frontend Changes\n")
    
    try:
        # Try different possible paths
        possible_paths = [
            'expense-frontend/src/pages/Groups.tsx',
            '../expense-frontend/src/pages/Groups.tsx',
            '../../expense-frontend/src/pages/Groups.tsx'
        ]

        content = None
        for path in possible_paths:
            try:
                with open(path, 'r') as f:
                    content = f.read()
                    break
            except FileNotFoundError:
                continue

        if content is None:
            print("❌ Frontend file not found in any expected location")
            return False
            
        checks = [
            ('groupManagementAPI import', 'groupManagementAPI' in content),
            ('requestToJoin usage', 'requestToJoin' in content),
            ('joinMessage state', 'joinMessage' in content),
            ('Send Request button', 'Send Request' in content),
            ('Request to Join button', 'Request to Join' in content),
            ('Join request sent message', 'Join request sent successfully' in content),
        ]
        
        print("Frontend file checks:")
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
                
        return all_passed

    except Exception as e:
        print(f"❌ Error checking frontend file: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Updated Join Request Workflow\n")
    
    # Run all tests
    workflow_test = test_join_request_workflow()
    duplicate_test = test_duplicate_request_prevention()
    frontend_test = verify_frontend_changes()
    
    print(f"\n📊 Test Results:")
    print(f"   Join Request Workflow: {'✅ PASSED' if workflow_test else '❌ FAILED'}")
    print(f"   Duplicate Prevention: {'✅ PASSED' if duplicate_test else '❌ FAILED'}")
    print(f"   Frontend Updates: {'✅ PASSED' if frontend_test else '❌ FAILED'}")
    
    if workflow_test and duplicate_test and frontend_test:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ Groups page now uses join request system")
        print("✅ Join requests require owner approval")
        print("✅ Frontend properly updated")
        print("✅ Workflow is consistent across the application")
    else:
        print(f"\n💥 SOME TESTS FAILED!")
        print("⚠️ Please check the issues above")
