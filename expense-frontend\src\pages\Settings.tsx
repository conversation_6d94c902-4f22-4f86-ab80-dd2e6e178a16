/**
 * Comprehensive Settings page with all user preferences and account management
 */

import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import {
  User,
  Lock,
  Bell,
  Palette,
  Globe,
  Download,
  Trash2,
  Key,
  Save,
  Eye,
  EyeOff,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Moon,
  Sun,
  DollarSign
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { settingsAPI } from '../services/api';
import toast from 'react-hot-toast';

interface NotificationPreferences {
  expense_created: boolean;
  expense_approved: boolean;
  settlement_received: boolean;
  settlement_confirmed: boolean;
  join_request_processed: boolean;
  email_notifications: boolean;
}

interface DisplaySettings {
  theme: 'light' | 'dark' | 'system';
  currency: string;
  language: string;
  date_format: string;
}

const Settings: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'notifications' | 'display' | 'data' | 'account'>('profile');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Profile settings
  const [profileData, setProfileData] = useState({
    name: user?.email?.split('@')[0] || '',
    email: user?.email || ''
  });

  // Security settings
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [groqApiKey, setGroqApiKey] = useState('');

  // Notification preferences
  const [notificationPrefs, setNotificationPrefs] = useState<NotificationPreferences>({
    expense_created: true,
    expense_approved: true,
    settlement_received: true,
    settlement_confirmed: true,
    join_request_processed: true,
    email_notifications: false
  });

  // Display settings
  const [displaySettings, setDisplaySettings] = useState<DisplaySettings>({
    theme: 'light',
    currency: 'USD',
    language: 'en',
    date_format: 'MM/DD/YYYY'
  });

  // Account deletion
  const [deleteConfirmation, setDeleteConfirmation] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      // Load notification preferences
      try {
        const notifResponse = await settingsAPI.getNotificationPreferences();
        setNotificationPrefs(notifResponse.data);
      } catch (error) {
        console.log('Notification preferences not available yet');
      }

      // Load display settings
      try {
        const displayResponse = await settingsAPI.getDisplaySettings();
        setDisplaySettings(displayResponse.data);
      } catch (error) {
        console.log('Display settings not available yet');
      }

    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async () => {
    if (!profileData.name.trim() || !profileData.email.trim()) {
      toast.error('Name and email are required');
      return;
    }

    try {
      setSaving(true);
      await settingsAPI.updateProfile(profileData);
      toast.success('Profile updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleChangePassword = async () => {
    if (!passwordData.current_password || !passwordData.new_password) {
      toast.error('Current and new passwords are required');
      return;
    }

    if (passwordData.new_password !== passwordData.confirm_password) {
      toast.error('New passwords do not match');
      return;
    }

    if (passwordData.new_password.length < 6) {
      toast.error('New password must be at least 6 characters');
      return;
    }

    try {
      setSaving(true);
      await settingsAPI.changePassword({
        current_password: passwordData.current_password,
        new_password: passwordData.new_password
      });
      
      setPasswordData({ current_password: '', new_password: '', confirm_password: '' });
      toast.success('Password changed successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to change password');
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateGroqApiKey = async () => {
    if (!groqApiKey.trim()) {
      toast.error('Groq API key is required');
      return;
    }

    try {
      setSaving(true);
      await settingsAPI.updateGroqApiKey({ groq_api_key: groqApiKey });
      toast.success('Groq API key updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to update API key');
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateNotifications = async () => {
    try {
      setSaving(true);
      await settingsAPI.updateNotificationPreferences(notificationPrefs);
      toast.success('Notification preferences updated');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to update notifications');
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateDisplay = async () => {
    try {
      setSaving(true);
      await settingsAPI.updateDisplaySettings(displaySettings);
      toast.success('Display settings updated');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to update display settings');
    } finally {
      setSaving(false);
    }
  };

  const handleExportData = async () => {
    try {
      setSaving(true);
      const response = await settingsAPI.exportData();
      
      // Create and download file
      const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `expense-tracker-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Data exported successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to export data');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (deleteConfirmation !== 'DELETE') {
      toast.error('Please type DELETE to confirm');
      return;
    }

    if (!passwordData.current_password) {
      toast.error('Password is required to delete account');
      return;
    }

    try {
      setSaving(true);
      await settingsAPI.deleteAccount(passwordData.current_password);
      toast.success('Account deleted successfully');
      logout();
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to delete account');
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'security', label: 'Security', icon: Lock },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'display', label: 'Display', icon: Palette },
    { id: 'data', label: 'Data', icon: Download },
    { id: 'account', label: 'Account', icon: Trash2 }
  ];

  return (
    <Layout title="Settings" subtitle="Manage your account preferences and settings">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              </div>
            ) : (
              <>
                {/* Profile Tab */}
                {activeTab === 'profile' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Profile Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Display Name
                          </label>
                          <input
                            type="text"
                            value={profileData.name}
                            onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
                            className="input-field"
                            placeholder="Enter your display name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                          </label>
                          <input
                            type="email"
                            value={profileData.email}
                            onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                            className="input-field"
                            placeholder="Enter your email"
                          />
                        </div>
                      </div>
                      <div className="mt-6">
                        <button
                          onClick={handleUpdateProfile}
                          disabled={saving}
                          className="btn-primary"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {saving ? 'Saving...' : 'Save Profile'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Security Tab */}
                {activeTab === 'security' && (
                  <div className="space-y-8">
                    {/* Change Password */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
                      <div className="space-y-4 max-w-md">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Current Password
                          </label>
                          <div className="relative">
                            <input
                              type={showPasswords.current ? 'text' : 'password'}
                              value={passwordData.current_password}
                              onChange={(e) => setPasswordData({ ...passwordData, current_password: e.target.value })}
                              className="input-field pr-10"
                              placeholder="Enter current password"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                              {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </button>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            New Password
                          </label>
                          <div className="relative">
                            <input
                              type={showPasswords.new ? 'text' : 'password'}
                              value={passwordData.new_password}
                              onChange={(e) => setPasswordData({ ...passwordData, new_password: e.target.value })}
                              className="input-field pr-10"
                              placeholder="Enter new password"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                              {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </button>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Confirm New Password
                          </label>
                          <div className="relative">
                            <input
                              type={showPasswords.confirm ? 'text' : 'password'}
                              value={passwordData.confirm_password}
                              onChange={(e) => setPasswordData({ ...passwordData, confirm_password: e.target.value })}
                              className="input-field pr-10"
                              placeholder="Confirm new password"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                              {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </button>
                          </div>
                        </div>
                        <button
                          onClick={handleChangePassword}
                          disabled={saving}
                          className="btn-primary"
                        >
                          <Lock className="w-4 h-4 mr-2" />
                          {saving ? 'Changing...' : 'Change Password'}
                        </button>
                      </div>
                    </div>

                    {/* API Key Management */}
                    <div className="border-t border-gray-200 pt-8">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">API Key Management</h3>
                      <div className="max-w-md">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Groq API Key
                        </label>
                        <input
                          type="password"
                          value={groqApiKey}
                          onChange={(e) => setGroqApiKey(e.target.value)}
                          className="input-field"
                          placeholder="Enter your Groq API key"
                        />
                        <p className="text-sm text-gray-600 mt-2">
                          Your Groq API key is used for AI-powered expense processing.
                        </p>
                        <button
                          onClick={handleUpdateGroqApiKey}
                          disabled={saving}
                          className="btn-primary mt-4"
                        >
                          <Key className="w-4 h-4 mr-2" />
                          {saving ? 'Updating...' : 'Update API Key'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Notifications Tab */}
                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">Expense Created</h4>
                            <p className="text-sm text-gray-600">Get notified when you create new expenses</p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={notificationPrefs.expense_created}
                              onChange={(e) => setNotificationPrefs({ ...notificationPrefs, expense_created: e.target.checked })}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">Expense Approved</h4>
                            <p className="text-sm text-gray-600">Get notified when expenses are approved</p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={notificationPrefs.expense_approved}
                              onChange={(e) => setNotificationPrefs({ ...notificationPrefs, expense_approved: e.target.checked })}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">Settlement Received</h4>
                            <p className="text-sm text-gray-600">Get notified when you receive settlement payments</p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={notificationPrefs.settlement_received}
                              onChange={(e) => setNotificationPrefs({ ...notificationPrefs, settlement_received: e.target.checked })}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">Settlement Confirmed</h4>
                            <p className="text-sm text-gray-600">Get notified when settlements are confirmed</p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={notificationPrefs.settlement_confirmed}
                              onChange={(e) => setNotificationPrefs({ ...notificationPrefs, settlement_confirmed: e.target.checked })}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">Join Request Updates</h4>
                            <p className="text-sm text-gray-600">Get notified about group join request responses</p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={notificationPrefs.join_request_processed}
                              onChange={(e) => setNotificationPrefs({ ...notificationPrefs, join_request_processed: e.target.checked })}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>

                        <div className="border-t border-gray-200 pt-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                              <p className="text-sm text-gray-600">Receive notifications via email (coming soon)</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationPrefs.email_notifications}
                                onChange={(e) => setNotificationPrefs({ ...notificationPrefs, email_notifications: e.target.checked })}
                                className="sr-only peer"
                                disabled
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600 opacity-50"></div>
                            </label>
                          </div>
                        </div>
                      </div>
                      <div className="mt-6">
                        <button
                          onClick={handleUpdateNotifications}
                          disabled={saving}
                          className="btn-primary"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {saving ? 'Saving...' : 'Save Preferences'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Display Tab */}
                {activeTab === 'display' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Display Settings</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Theme
                          </label>
                          <select
                            value={displaySettings.theme}
                            onChange={(e) => setDisplaySettings({ ...displaySettings, theme: e.target.value as any })}
                            className="input-field"
                          >
                            <option value="light">Light</option>
                            <option value="dark">Dark (Coming Soon)</option>
                            <option value="system">System (Coming Soon)</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Currency
                          </label>
                          <select
                            value={displaySettings.currency}
                            onChange={(e) => setDisplaySettings({ ...displaySettings, currency: e.target.value })}
                            className="input-field"
                          >
                            <option value="USD">USD ($)</option>
                            <option value="PKR">PKR (₨)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="JPY">JPY (¥)</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Language
                          </label>
                          <select
                            value={displaySettings.language}
                            onChange={(e) => setDisplaySettings({ ...displaySettings, language: e.target.value })}
                            className="input-field"
                          >
                            <option value="en">English</option>
                            <option value="ur">Urdu (Coming Soon)</option>
                            <option value="es">Spanish (Coming Soon)</option>
                            <option value="fr">French (Coming Soon)</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Date Format
                          </label>
                          <select
                            value={displaySettings.date_format}
                            onChange={(e) => setDisplaySettings({ ...displaySettings, date_format: e.target.value })}
                            className="input-field"
                          >
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                          </select>
                        </div>
                      </div>
                      <div className="mt-6">
                        <button
                          onClick={handleUpdateDisplay}
                          disabled={saving}
                          className="btn-primary"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {saving ? 'Saving...' : 'Save Settings'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Data Tab */}
                {activeTab === 'data' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Data Management</h3>
                      
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div className="flex items-start space-x-3">
                          <Download className="w-5 h-5 text-blue-600 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-blue-900">Export Your Data</h4>
                            <p className="text-sm text-blue-700 mt-1">
                              Download all your expense data, groups, and settings in JSON format.
                            </p>
                            <button
                              onClick={handleExportData}
                              disabled={saving}
                              className="btn-primary mt-3"
                            >
                              <Download className="w-4 h-4 mr-2" />
                              {saving ? 'Exporting...' : 'Export Data'}
                            </button>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <Globe className="w-5 h-5 text-gray-600 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-gray-900">Import Data</h4>
                            <p className="text-sm text-gray-600 mt-1">
                              Import expense data from other platforms (coming soon).
                            </p>
                            <button
                              disabled
                              className="bg-gray-300 text-gray-500 cursor-not-allowed px-4 py-2 rounded-lg mt-3"
                            >
                              Import Data (Coming Soon)
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Account Tab */}
                {activeTab === 'account' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Account Management</h3>
                      
                      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div className="flex items-start space-x-3">
                          <AlertTriangle className="w-6 h-6 text-red-600 mt-0.5" />
                          <div className="flex-1">
                            <h4 className="font-medium text-red-900">Delete Account</h4>
                            <p className="text-sm text-red-700 mt-1">
                              Permanently delete your account and all associated data. This action cannot be undone.
                            </p>
                            
                            {!showDeleteConfirm ? (
                              <button
                                onClick={() => setShowDeleteConfirm(true)}
                                className="btn-danger mt-4"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete Account
                              </button>
                            ) : (
                              <div className="mt-4 space-y-4">
                                <div>
                                  <label className="block text-sm font-medium text-red-900 mb-2">
                                    Type "DELETE" to confirm
                                  </label>
                                  <input
                                    type="text"
                                    value={deleteConfirmation}
                                    onChange={(e) => setDeleteConfirmation(e.target.value)}
                                    className="input-field max-w-xs"
                                    placeholder="Type DELETE"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-red-900 mb-2">
                                    Enter your password
                                  </label>
                                  <input
                                    type="password"
                                    value={passwordData.current_password}
                                    onChange={(e) => setPasswordData({ ...passwordData, current_password: e.target.value })}
                                    className="input-field max-w-xs"
                                    placeholder="Enter password"
                                  />
                                </div>
                                <div className="flex space-x-3">
                                  <button
                                    onClick={handleDeleteAccount}
                                    disabled={saving || deleteConfirmation !== 'DELETE'}
                                    className="btn-danger"
                                  >
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    {saving ? 'Deleting...' : 'Confirm Delete'}
                                  </button>
                                  <button
                                    onClick={() => {
                                      setShowDeleteConfirm(false);
                                      setDeleteConfirmation('');
                                      setPasswordData({ ...passwordData, current_password: '' });
                                    }}
                                    className="btn-secondary"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
