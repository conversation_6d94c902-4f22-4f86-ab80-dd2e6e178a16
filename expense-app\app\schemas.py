from pydantic import BaseModel, EmailStr
from decimal import Decimal
from typing import List

class UserCreate(BaseModel):
    email: EmailStr
    password: str
    groq_api_key: str

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123",
                "groq_api_key": "gsk_your_groq_api_key_here"
            }
        }

class UserOut(BaseModel):
    id: int
    email: EmailStr

    class Config:
        from_attributes = True 

class Token(BaseModel):
    access_token: str
    token_type: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123"
            }
        }

from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class GroupBase(BaseModel):
    name: str

class GroupCreate(GroupBase):
    pass

class GroupResponse(GroupBase):
    id: int
    creator_id: int
    members: List[UserOut] = []

    class Config:
        from_attributes = True 

class JoinGroup(BaseModel):
    group_id: int

class ExpenseCreate(BaseModel):
    group_id: int
    total: Decimal
    description: str

    class Config:
        json_schema_extra = {
            "example": {
                "group_id": 1,
                "total": 25.50,
                "description": "Lunch at restaurant"
            }
        }

class ShareOut(BaseModel):
    user_id: int
    amount: Decimal
    paid: bool

    class Config:
        from_attributes = True 

class ExpenseOut(BaseModel):
    id: int
    payer_id: int
    group_id: int
    total: Decimal
    description: str
    shares: List[ShareOut]

    class Config:
        from_attributes = True 

class Balance(BaseModel):
    user_id: int
    email: EmailStr
    amount: Decimal  # positive: they owe you; negative: you owe them

    class Config:
        from_attributes = True 


class SettlementRequest(BaseModel):
    target_user_id: int    # who you’re paying
    amount: Decimal        # how much you’re paying

class SettlementResult(BaseModel):
    share_id: int
    paid_amount: Decimal   # how much was applied to this share
    remaining_amount: Decimal  # what’s left on that share

    class Config:
        from_attributes = True

class SettlementSummary(BaseModel):
    total_paid: Decimal
    target_user_email: str
    settlements: List[SettlementResult]
    message: str

    class Config:
        from_attributes = True

# NLP Schemas
class NLPRequest(BaseModel):
    message: str

    class Config:
        json_schema_extra = {
            "example": {
                "message": "Add lunch 25 split among roommates"
            }
        }

class NLPResponse(BaseModel):
    success: bool
    intent: str
    message: str
    result: Optional[dict] = None

    class Config:
        from_attributes = True

# Expense Approval Schemas
class ExpenseApprovalBase(BaseModel):
    approved: Optional[bool] = None

class ExpenseApprovalCreate(ExpenseApprovalBase):
    expense_id: int

class ExpenseApprovalUpdate(BaseModel):
    approved: bool

class ExpenseApprovalOut(ExpenseApprovalBase):
    id: int
    expense_id: int
    user_id: int
    approved_at: Optional[datetime] = None
    created_at: datetime
    user: UserOut

    class Config:
        from_attributes = True

# Settlement Schemas
class SettlementBase(BaseModel):
    amount: float
    description: Optional[str] = None

class SettlementCreate(SettlementBase):
    recipient_id: int

class SettlementUpdate(BaseModel):
    status: str

class SettlementOut(SettlementBase):
    id: int
    payer_id: int
    recipient_id: int
    status: str
    created_at: datetime
    confirmed_at: Optional[datetime] = None
    payer: UserOut
    recipient: UserOut

    class Config:
        from_attributes = True

# Updated Expense Schema with Approval Status
class ExpenseOutWithApprovals(ExpenseOut):
    status: str
    approved_at: Optional[datetime] = None
    approvals: List[ExpenseApprovalOut] = []

    class Config:
        from_attributes = True

# Settings schemas
class UserProfileUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None

class PasswordChange(BaseModel):
    current_password: str
    new_password: str

class GroqApiKeyUpdate(BaseModel):
    groq_api_key: str

class NotificationPreferences(BaseModel):
    expense_created: bool = True
    expense_approved: bool = True
    settlement_received: bool = True
    settlement_confirmed: bool = True
    join_request_processed: bool = True
    email_notifications: bool = False

class DisplaySettings(BaseModel):
    theme: Optional[str] = None
    currency: Optional[str] = None
    language: Optional[str] = None
    date_format: Optional[str] = None

class AccountDeletion(BaseModel):
    password: str

# Group management schemas
class TransferOwnershipRequest(BaseModel):
    new_owner_id: int
