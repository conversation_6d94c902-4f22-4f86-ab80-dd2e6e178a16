#!/usr/bin/env python3
"""
Test the actual API endpoint for can-leave functionality
"""

import requests
import json
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'expense-app'))
from app.database import SessionLocal
from app.models import User, Group

def get_auth_token(email: str, password: str = "testpassword123"):
    """Get JWT token for authentication"""
    try:
        response = requests.post("http://localhost:8000/auth/login", data={
            "username": email,
            "password": password
        })
        if response.status_code == 200:
            return response.json()["access_token"]
        else:
            print(f"Failed to authenticate {email}: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error authenticating: {e}")
        return None

def test_can_leave_endpoint():
    """Test the /group-management/groups/{group_id}/can-leave endpoint"""
    print("🔧 Testing Can-Leave API Endpoint\n")
    
    # Get test users and their group
    db = SessionLocal()
    try:
        user1 = db.query(User).filter(User.email == '<EMAIL>').first()
        user2 = db.query(User).filter(User.email == '<EMAIL>').first()
        
        if not user1 or not user2:
            print("❌ Test users not found")
            return False
            
        # Find their shared group
        shared_groups = []
        for group in user1.groups:
            if user2 in group.members:
                shared_groups.append(group)
        
        if not shared_groups:
            print("❌ No shared groups found")
            return False
            
        group = shared_groups[0]
        print(f"Testing group: {group.name} (ID: {group.id})")
        
    finally:
        db.close()
    
    # Test for both users
    for user_email in ['<EMAIL>', '<EMAIL>']:
        print(f"\n👤 Testing for {user_email}:")
        
        # Get auth token
        token = get_auth_token(user_email)
        if not token:
            print(f"❌ Could not authenticate {user_email}")
            continue
            
        # Test can-leave endpoint
        headers = {"Authorization": f"Bearer {token}"}
        try:
            response = requests.get(
                f"http://localhost:8000/group-management/groups/{group.id}/can-leave",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API Response:")
                print(f"   Can leave: {data['can_leave']}")
                print(f"   Balance: ${data['balance']:.2f}")
                print(f"   Message: {data['message']}")
                
                if data['can_leave']:
                    print("   🎉 User can leave the group!")
                else:
                    print("   ⚠️ User cannot leave the group")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
    
    return True

if __name__ == "__main__":
    test_can_leave_endpoint()
