{"ast": null, "code": "/**\n * Real-time notification service for expense tracking application\n */\n\nimport toast from 'react-hot-toast';\nclass NotificationService {\n  constructor() {\n    this.notifications = [];\n    this.listeners = [];\n    this.refreshCallbacks = [];\n    // Initialize with some mock notifications for demo\n    this.addNotification({\n      type: 'expense_added',\n      title: 'New Expense Added',\n      message: '<PERSON> added lunch expense for $25.00',\n      data: {\n        expenseId: 1,\n        amount: 25.00\n      }\n    });\n    this.addNotification({\n      type: 'approval_required',\n      title: 'Approval Required',\n      message: 'Sarah added dinner expense for $45.00 - requires your approval',\n      data: {\n        expenseId: 2,\n        amount: 45.00\n      }\n    });\n  }\n  addNotification(notification) {\n    const newNotification = {\n      ...notification,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      timestamp: new Date(),\n      read: false\n    };\n    this.notifications.unshift(newNotification);\n\n    // Keep only last 50 notifications\n    if (this.notifications.length > 50) {\n      this.notifications = this.notifications.slice(0, 50);\n    }\n\n    // Show toast notification\n    this.showToast(newNotification);\n\n    // Notify listeners\n    this.notifyListeners();\n\n    // Trigger data refresh\n    this.triggerRefresh();\n  }\n  showToast(notification) {\n    const toastOptions = {\n      duration: 4000,\n      position: 'top-right'\n    };\n    switch (notification.type) {\n      case 'expense_added':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'approval_required':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '⏳'\n        });\n        break;\n      case 'settlement_pending':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '💰'\n        });\n        break;\n      case 'request_approved':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'request_rejected':\n        toast.error(notification.message, toastOptions);\n        break;\n      default:\n        toast(notification.message, toastOptions);\n    }\n  }\n  getNotifications() {\n    return [...this.notifications];\n  }\n  getUnreadCount() {\n    return this.notifications.filter(n => !n.read).length;\n  }\n  markAsRead(notificationId) {\n    const notification = this.notifications.find(n => n.id === notificationId);\n    if (notification) {\n      notification.read = true;\n      this.notifyListeners();\n    }\n  }\n  markAllAsRead() {\n    this.notifications.forEach(n => n.read = true);\n    this.notifyListeners();\n  }\n  subscribe(callback) {\n    this.listeners.push(callback);\n\n    // Return unsubscribe function\n    return () => {\n      this.listeners = this.listeners.filter(listener => listener !== callback);\n    };\n  }\n  subscribeToRefresh(callback) {\n    this.refreshCallbacks.push(callback);\n\n    // Return unsubscribe function\n    return () => {\n      this.refreshCallbacks = this.refreshCallbacks.filter(cb => cb !== callback);\n    };\n  }\n  notifyListeners() {\n    this.listeners.forEach(listener => listener([...this.notifications]));\n  }\n  triggerRefresh() {\n    this.refreshCallbacks.forEach(callback => callback());\n  }\n\n  // Simulate real-time events for demo purposes\n  simulateExpenseAdded(expenseData) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'New Expense Added',\n      message: `${expenseData.payer} added ${expenseData.description} for $${expenseData.amount.toFixed(2)}`,\n      data: expenseData\n    });\n  }\n  simulateApprovalRequired(expenseData) {\n    this.addNotification({\n      type: 'approval_required',\n      title: 'Approval Required',\n      message: `${expenseData.payer} added ${expenseData.description} for $${expenseData.amount.toFixed(2)} - requires your approval`,\n      data: expenseData\n    });\n  }\n  simulateSettlementPending(settlementData) {\n    this.addNotification({\n      type: 'settlement_pending',\n      title: 'Settlement Confirmation',\n      message: `${settlementData.payer} sent you $${settlementData.amount.toFixed(2)} - please confirm receipt`,\n      data: settlementData\n    });\n  }\n  simulateRequestApproved(requestData) {\n    this.addNotification({\n      type: 'request_approved',\n      title: 'Request Approved',\n      message: `Your request to join \"${requestData.groupName}\" has been approved`,\n      data: requestData\n    });\n  }\n  simulateRequestRejected(requestData) {\n    this.addNotification({\n      type: 'request_rejected',\n      title: 'Request Rejected',\n      message: `Your request to join \"${requestData.groupName}\" has been rejected`,\n      data: requestData\n    });\n  }\n}\n\n// Create singleton instance\nexport const notificationService = new NotificationService();\n\n// Export types\n\nexport default notificationService;", "map": {"version": 3, "names": ["toast", "NotificationService", "constructor", "notifications", "listeners", "refreshCallbacks", "addNotification", "type", "title", "message", "data", "expenseId", "amount", "notification", "newNotification", "id", "Date", "now", "toString", "Math", "random", "substr", "timestamp", "read", "unshift", "length", "slice", "showToast", "notifyListeners", "triggerRefresh", "toastOptions", "duration", "position", "success", "icon", "error", "getNotifications", "getUnreadCount", "filter", "n", "mark<PERSON><PERSON><PERSON>", "notificationId", "find", "markAllAsRead", "for<PERSON>ach", "subscribe", "callback", "push", "listener", "subscribeToRefresh", "cb", "simulateExpenseAdded", "expenseData", "payer", "description", "toFixed", "simulateApprovalRequired", "simulateSettlementPending", "settlementData", "simulateRequestApproved", "requestData", "groupName", "simulateRequestRejected", "notificationService"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/services/notificationService.ts"], "sourcesContent": ["/**\n * Real-time notification service for expense tracking application\n */\n\nimport toast from 'react-hot-toast';\n\nexport interface AppNotification {\n  id: string;\n  type: 'expense_added' | 'approval_required' | 'settlement_pending' | 'request_approved' | 'request_rejected';\n  title: string;\n  message: string;\n  timestamp: Date;\n  read: boolean;\n  data?: any;\n}\n\nclass NotificationService {\n  private notifications: AppNotification[] = [];\n  private listeners: ((notifications: AppNotification[]) => void)[] = [];\n  private refreshCallbacks: (() => void)[] = [];\n\n  constructor() {\n    // Initialize with some mock notifications for demo\n    this.addNotification({\n      type: 'expense_added',\n      title: 'New Expense Added',\n      message: '<PERSON> added lunch expense for $25.00',\n      data: { expenseId: 1, amount: 25.00 }\n    });\n\n    this.addNotification({\n      type: 'approval_required',\n      title: 'Approval Required',\n      message: 'Sarah added dinner expense for $45.00 - requires your approval',\n      data: { expenseId: 2, amount: 45.00 }\n    });\n  }\n\n  addNotification(notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) {\n    const newNotification: AppNotification = {\n      ...notification,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      timestamp: new Date(),\n      read: false\n    };\n\n    this.notifications.unshift(newNotification);\n    \n    // Keep only last 50 notifications\n    if (this.notifications.length > 50) {\n      this.notifications = this.notifications.slice(0, 50);\n    }\n\n    // Show toast notification\n    this.showToast(newNotification);\n\n    // Notify listeners\n    this.notifyListeners();\n\n    // Trigger data refresh\n    this.triggerRefresh();\n  }\n\n  private showToast(notification: AppNotification) {\n    const toastOptions = {\n      duration: 4000,\n      position: 'top-right' as const,\n    };\n\n    switch (notification.type) {\n      case 'expense_added':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'approval_required':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '⏳',\n        });\n        break;\n      case 'settlement_pending':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '💰',\n        });\n        break;\n      case 'request_approved':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'request_rejected':\n        toast.error(notification.message, toastOptions);\n        break;\n      default:\n        toast(notification.message, toastOptions);\n    }\n  }\n\n  getNotifications(): AppNotification[] {\n    return [...this.notifications];\n  }\n\n  getUnreadCount(): number {\n    return this.notifications.filter(n => !n.read).length;\n  }\n\n  markAsRead(notificationId: string) {\n    const notification = this.notifications.find(n => n.id === notificationId);\n    if (notification) {\n      notification.read = true;\n      this.notifyListeners();\n    }\n  }\n\n  markAllAsRead() {\n    this.notifications.forEach(n => n.read = true);\n    this.notifyListeners();\n  }\n\n  subscribe(callback: (notifications: AppNotification[]) => void) {\n    this.listeners.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.listeners = this.listeners.filter(listener => listener !== callback);\n    };\n  }\n\n  subscribeToRefresh(callback: () => void) {\n    this.refreshCallbacks.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.refreshCallbacks = this.refreshCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  private notifyListeners() {\n    this.listeners.forEach(listener => listener([...this.notifications]));\n  }\n\n  private triggerRefresh() {\n    this.refreshCallbacks.forEach(callback => callback());\n  }\n\n  // Simulate real-time events for demo purposes\n  simulateExpenseAdded(expenseData: { description: string; amount: number; payer: string }) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'New Expense Added',\n      message: `${expenseData.payer} added ${expenseData.description} for $${expenseData.amount.toFixed(2)}`,\n      data: expenseData\n    });\n  }\n\n  simulateApprovalRequired(expenseData: { description: string; amount: number; payer: string }) {\n    this.addNotification({\n      type: 'approval_required',\n      title: 'Approval Required',\n      message: `${expenseData.payer} added ${expenseData.description} for $${expenseData.amount.toFixed(2)} - requires your approval`,\n      data: expenseData\n    });\n  }\n\n  simulateSettlementPending(settlementData: { amount: number; payer: string }) {\n    this.addNotification({\n      type: 'settlement_pending',\n      title: 'Settlement Confirmation',\n      message: `${settlementData.payer} sent you $${settlementData.amount.toFixed(2)} - please confirm receipt`,\n      data: settlementData\n    });\n  }\n\n  simulateRequestApproved(requestData: { groupName: string }) {\n    this.addNotification({\n      type: 'request_approved',\n      title: 'Request Approved',\n      message: `Your request to join \"${requestData.groupName}\" has been approved`,\n      data: requestData\n    });\n  }\n\n  simulateRequestRejected(requestData: { groupName: string }) {\n    this.addNotification({\n      type: 'request_rejected',\n      title: 'Request Rejected',\n      message: `Your request to join \"${requestData.groupName}\" has been rejected`,\n      data: requestData\n    });\n  }\n}\n\n// Create singleton instance\nexport const notificationService = new NotificationService();\n\n// Export types\nexport type { AppNotification };\nexport default notificationService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,iBAAiB;AAYnC,MAAMC,mBAAmB,CAAC;EAKxBC,WAAWA,CAAA,EAAG;IAAA,KAJNC,aAAa,GAAsB,EAAE;IAAA,KACrCC,SAAS,GAAmD,EAAE;IAAA,KAC9DC,gBAAgB,GAAmB,EAAE;IAG3C;IACA,IAAI,CAACC,eAAe,CAAC;MACnBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,qCAAqC;MAC9CC,IAAI,EAAE;QAAEC,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAM;IACtC,CAAC,CAAC;IAEF,IAAI,CAACN,eAAe,CAAC;MACnBC,IAAI,EAAE,mBAAmB;MACzBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,gEAAgE;MACzEC,IAAI,EAAE;QAAEC,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAM;IACtC,CAAC,CAAC;EACJ;EAEAN,eAAeA,CAACO,YAAgE,EAAE;IAChF,MAAMC,eAAgC,GAAG;MACvC,GAAGD,YAAY;MACfE,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACnEC,SAAS,EAAE,IAAIN,IAAI,CAAC,CAAC;MACrBO,IAAI,EAAE;IACR,CAAC;IAED,IAAI,CAACpB,aAAa,CAACqB,OAAO,CAACV,eAAe,CAAC;;IAE3C;IACA,IAAI,IAAI,CAACX,aAAa,CAACsB,MAAM,GAAG,EAAE,EAAE;MAClC,IAAI,CAACtB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACuB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACtD;;IAEA;IACA,IAAI,CAACC,SAAS,CAACb,eAAe,CAAC;;IAE/B;IACA,IAAI,CAACc,eAAe,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEQF,SAASA,CAACd,YAA6B,EAAE;IAC/C,MAAMiB,YAAY,GAAG;MACnBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC;IAED,QAAQnB,YAAY,CAACN,IAAI;MACvB,KAAK,eAAe;QAClBP,KAAK,CAACiC,OAAO,CAACpB,YAAY,CAACJ,OAAO,EAAEqB,YAAY,CAAC;QACjD;MACF,KAAK,mBAAmB;QACtB9B,KAAK,CAACa,YAAY,CAACJ,OAAO,EAAE;UAC1B,GAAGqB,YAAY;UACfI,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF,KAAK,oBAAoB;QACvBlC,KAAK,CAACa,YAAY,CAACJ,OAAO,EAAE;UAC1B,GAAGqB,YAAY;UACfI,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF,KAAK,kBAAkB;QACrBlC,KAAK,CAACiC,OAAO,CAACpB,YAAY,CAACJ,OAAO,EAAEqB,YAAY,CAAC;QACjD;MACF,KAAK,kBAAkB;QACrB9B,KAAK,CAACmC,KAAK,CAACtB,YAAY,CAACJ,OAAO,EAAEqB,YAAY,CAAC;QAC/C;MACF;QACE9B,KAAK,CAACa,YAAY,CAACJ,OAAO,EAAEqB,YAAY,CAAC;IAC7C;EACF;EAEAM,gBAAgBA,CAAA,EAAsB;IACpC,OAAO,CAAC,GAAG,IAAI,CAACjC,aAAa,CAAC;EAChC;EAEAkC,cAAcA,CAAA,EAAW;IACvB,OAAO,IAAI,CAAClC,aAAa,CAACmC,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAChB,IAAI,CAAC,CAACE,MAAM;EACvD;EAEAe,UAAUA,CAACC,cAAsB,EAAE;IACjC,MAAM5B,YAAY,GAAG,IAAI,CAACV,aAAa,CAACuC,IAAI,CAACH,CAAC,IAAIA,CAAC,CAACxB,EAAE,KAAK0B,cAAc,CAAC;IAC1E,IAAI5B,YAAY,EAAE;MAChBA,YAAY,CAACU,IAAI,GAAG,IAAI;MACxB,IAAI,CAACK,eAAe,CAAC,CAAC;IACxB;EACF;EAEAe,aAAaA,CAAA,EAAG;IACd,IAAI,CAACxC,aAAa,CAACyC,OAAO,CAACL,CAAC,IAAIA,CAAC,CAAChB,IAAI,GAAG,IAAI,CAAC;IAC9C,IAAI,CAACK,eAAe,CAAC,CAAC;EACxB;EAEAiB,SAASA,CAACC,QAAoD,EAAE;IAC9D,IAAI,CAAC1C,SAAS,CAAC2C,IAAI,CAACD,QAAQ,CAAC;;IAE7B;IACA,OAAO,MAAM;MACX,IAAI,CAAC1C,SAAS,GAAG,IAAI,CAACA,SAAS,CAACkC,MAAM,CAACU,QAAQ,IAAIA,QAAQ,KAAKF,QAAQ,CAAC;IAC3E,CAAC;EACH;EAEAG,kBAAkBA,CAACH,QAAoB,EAAE;IACvC,IAAI,CAACzC,gBAAgB,CAAC0C,IAAI,CAACD,QAAQ,CAAC;;IAEpC;IACA,OAAO,MAAM;MACX,IAAI,CAACzC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACiC,MAAM,CAACY,EAAE,IAAIA,EAAE,KAAKJ,QAAQ,CAAC;IAC7E,CAAC;EACH;EAEQlB,eAAeA,CAAA,EAAG;IACxB,IAAI,CAACxB,SAAS,CAACwC,OAAO,CAACI,QAAQ,IAAIA,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC7C,aAAa,CAAC,CAAC,CAAC;EACvE;EAEQ0B,cAAcA,CAAA,EAAG;IACvB,IAAI,CAACxB,gBAAgB,CAACuC,OAAO,CAACE,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC;EACvD;;EAEA;EACAK,oBAAoBA,CAACC,WAAmE,EAAE;IACxF,IAAI,CAAC9C,eAAe,CAAC;MACnBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,GAAG2C,WAAW,CAACC,KAAK,UAAUD,WAAW,CAACE,WAAW,SAASF,WAAW,CAACxC,MAAM,CAAC2C,OAAO,CAAC,CAAC,CAAC,EAAE;MACtG7C,IAAI,EAAE0C;IACR,CAAC,CAAC;EACJ;EAEAI,wBAAwBA,CAACJ,WAAmE,EAAE;IAC5F,IAAI,CAAC9C,eAAe,CAAC;MACnBC,IAAI,EAAE,mBAAmB;MACzBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,GAAG2C,WAAW,CAACC,KAAK,UAAUD,WAAW,CAACE,WAAW,SAASF,WAAW,CAACxC,MAAM,CAAC2C,OAAO,CAAC,CAAC,CAAC,2BAA2B;MAC/H7C,IAAI,EAAE0C;IACR,CAAC,CAAC;EACJ;EAEAK,yBAAyBA,CAACC,cAAiD,EAAE;IAC3E,IAAI,CAACpD,eAAe,CAAC;MACnBC,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE,yBAAyB;MAChCC,OAAO,EAAE,GAAGiD,cAAc,CAACL,KAAK,cAAcK,cAAc,CAAC9C,MAAM,CAAC2C,OAAO,CAAC,CAAC,CAAC,2BAA2B;MACzG7C,IAAI,EAAEgD;IACR,CAAC,CAAC;EACJ;EAEAC,uBAAuBA,CAACC,WAAkC,EAAE;IAC1D,IAAI,CAACtD,eAAe,CAAC;MACnBC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAE,yBAAyBmD,WAAW,CAACC,SAAS,qBAAqB;MAC5EnD,IAAI,EAAEkD;IACR,CAAC,CAAC;EACJ;EAEAE,uBAAuBA,CAACF,WAAkC,EAAE;IAC1D,IAAI,CAACtD,eAAe,CAAC;MACnBC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAE,yBAAyBmD,WAAW,CAACC,SAAS,qBAAqB;MAC5EnD,IAAI,EAAEkD;IACR,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,OAAO,MAAMG,mBAAmB,GAAG,IAAI9D,mBAAmB,CAAC,CAAC;;AAE5D;;AAEA,eAAe8D,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}