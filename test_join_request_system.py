#!/usr/bin/env python3
"""
Comprehensive test for the Group Join Request System
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'expense-app'))

from app.database import SessionLocal
from app.services.group_management_service import GroupManagementService
from app.models import User, Group, GroupJoinRequest, JoinRequestStatus
from sqlalchemy import and_

def test_join_request_workflow():
    """Test the complete join request workflow"""
    db = SessionLocal()
    try:
        print("🔧 Testing Group Join Request System\n")
        
        # Get test users
        users = db.query(User).limit(3).all()
        if len(users) < 3:
            print("❌ Need at least 3 users for testing")
            return False
            
        group_owner = users[0]
        requester1 = users[1]
        requester2 = users[2]
        
        print(f"👤 Group Owner: {group_owner.email}")
        print(f"👤 Requester 1: {requester1.email}")
        print(f"👤 Requester 2: {requester2.email}")
        
        # Find a group owned by the first user
        owned_groups = db.query(Group).filter(Group.creator_id == group_owner.id).all()
        if not owned_groups:
            print("❌ No groups found for testing")
            return False
            
        test_group = owned_groups[0]
        print(f"🏢 Test Group: {test_group.name} (ID: {test_group.id})")
        
        # Check if requesters are already members
        current_member_ids = [member.id for member in test_group.members]
        if requester1.id in current_member_ids:
            print(f"⚠️ {requester1.email} is already a member")
        if requester2.id in current_member_ids:
            print(f"⚠️ {requester2.email} is already a member")
            
        group_service = GroupManagementService(db)
        
        print("\n=== Testing Join Request Creation ===")
        
        # Test 1: Create join request from requester1
        try:
            if requester1.id not in current_member_ids:
                join_request1 = group_service.request_to_join_group(
                    requester1, test_group.id, "Please let me join your group!"
                )
                print(f"✅ Join request created: ID {join_request1.id}")
                print(f"   Status: {join_request1.status}")
                print(f"   Message: {join_request1.message}")
            else:
                print(f"⏭️ Skipping request creation - {requester1.email} already a member")
        except ValueError as e:
            print(f"⚠️ Join request creation failed: {e}")
            
        # Test 2: Try duplicate request (should fail)
        try:
            if requester1.id not in current_member_ids:
                duplicate_request = group_service.request_to_join_group(
                    requester1, test_group.id, "Another request"
                )
                print("❌ Duplicate request should have failed!")
                return False
        except ValueError as e:
            print(f"✅ Duplicate request correctly rejected: {e}")
            
        # Test 3: Create join request from requester2
        try:
            if requester2.id not in current_member_ids:
                join_request2 = group_service.request_to_join_group(
                    requester2, test_group.id, "I'd like to join too!"
                )
                print(f"✅ Second join request created: ID {join_request2.id}")
            else:
                print(f"⏭️ Skipping second request - {requester2.email} already a member")
        except ValueError as e:
            print(f"⚠️ Second join request failed: {e}")
            
        print("\n=== Testing Join Request Retrieval ===")
        
        # Test 4: Get pending requests for group owner
        pending_requests = group_service.get_pending_join_requests(group_owner)
        print(f"✅ Found {len(pending_requests)} pending requests for owner")
        
        for request in pending_requests:
            user = db.query(User).filter(User.id == request.user_id).first()
            group = db.query(Group).filter(Group.id == request.group_id).first()
            print(f"   Request {request.id}: {user.email} -> {group.name}")
            print(f"   Message: {request.message}")
            print(f"   Created: {request.created_at}")
            
        # Test 5: Get pending requests for specific group
        group_specific_requests = group_service.get_pending_join_requests(group_owner, test_group.id)
        print(f"✅ Found {len(group_specific_requests)} pending requests for specific group")
        
        print("\n=== Testing Join Request Processing ===")
        
        if pending_requests:
            # Test 6: Approve first request
            first_request = pending_requests[0]
            try:
                approved_request = group_service.process_join_request(group_owner, first_request.id, True)
                print(f"✅ Request {first_request.id} approved successfully")
                print(f"   Status: {approved_request.status}")
                print(f"   Processed at: {approved_request.processed_at}")
                
                # Check if user was added to group
                db.refresh(test_group)
                new_member_ids = [member.id for member in test_group.members]
                if first_request.user_id in new_member_ids:
                    print(f"✅ User successfully added to group")
                else:
                    print(f"❌ User was not added to group")
                    
            except ValueError as e:
                print(f"❌ Request approval failed: {e}")
                
            # Test 7: Reject second request (if exists)
            if len(pending_requests) > 1:
                second_request = pending_requests[1]
                try:
                    rejected_request = group_service.process_join_request(group_owner, second_request.id, False)
                    print(f"✅ Request {second_request.id} rejected successfully")
                    print(f"   Status: {rejected_request.status}")
                except ValueError as e:
                    print(f"❌ Request rejection failed: {e}")
                    
        print("\n=== Testing Authorization ===")
        
        # Test 8: Try to process request as non-owner (should fail)
        remaining_requests = group_service.get_pending_join_requests(group_owner)
        if remaining_requests:
            try:
                unauthorized_process = group_service.process_join_request(
                    requester1, remaining_requests[0].id, True
                )
                print("❌ Non-owner should not be able to process requests!")
                return False
            except ValueError as e:
                print(f"✅ Non-owner correctly denied: {e}")
                
        print("\n=== Final Status ===")
        
        # Show final state
        all_requests = db.query(GroupJoinRequest).filter(
            GroupJoinRequest.group_id == test_group.id
        ).all()
        
        print(f"📊 Total requests for group {test_group.name}: {len(all_requests)}")
        for request in all_requests:
            user = db.query(User).filter(User.id == request.user_id).first()
            status_emoji = "⏳" if request.status == "pending" else "✅" if request.status == "approved" else "❌"
            print(f"   {status_emoji} {user.email}: {request.status}")
            
        db.refresh(test_group)
        print(f"👥 Current group members: {len(test_group.members)}")
        for member in test_group.members:
            owner_indicator = "👑" if member.id == test_group.creator_id else "👤"
            print(f"   {owner_indicator} {member.email}")
            
        return True
        
    finally:
        db.close()

def test_api_endpoints():
    """Test the API endpoints (basic structure check)"""
    print("\n🌐 Testing API Endpoint Structure")
    
    # Import the router to check endpoints
    from app.routers.group_management import router
    
    endpoints = []
    for route in router.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            endpoints.append(f"{list(route.methods)[0]} {route.path}")
            
    join_request_endpoints = [ep for ep in endpoints if 'join' in ep.lower()]
    
    print("📡 Join Request Related Endpoints:")
    for endpoint in join_request_endpoints:
        print(f"   {endpoint}")
        
    expected_endpoints = [
        "POST /groups/{group_id}/join-request",
        "GET /groups/join-requests", 
        "POST /groups/join-requests/{request_id}/process"
    ]
    
    found_endpoints = 0
    for expected in expected_endpoints:
        if any(expected.split()[1] in ep for ep in join_request_endpoints):
            found_endpoints += 1
            print(f"   ✅ Found: {expected}")
        else:
            print(f"   ❌ Missing: {expected}")
            
    print(f"\n📊 API Endpoint Coverage: {found_endpoints}/{len(expected_endpoints)}")
    return found_endpoints == len(expected_endpoints)

if __name__ == "__main__":
    print("🚀 Comprehensive Group Join Request System Test\n")
    
    workflow_success = test_join_request_workflow()
    api_success = test_api_endpoints()
    
    print(f"\n{'🎉 ALL TESTS PASSED!' if workflow_success and api_success else '💥 SOME TESTS FAILED!'}")
    
    if workflow_success and api_success:
        print("\n✅ The join request system is fully functional!")
        print("✅ Users can request to join groups")
        print("✅ Group owners can approve/reject requests")
        print("✅ Authorization is properly enforced")
        print("✅ API endpoints are available")
    else:
        print("\n⚠️ Some issues were found that need attention")
