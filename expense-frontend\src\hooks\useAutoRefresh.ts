/**
 * Hook for automatic data refreshing when notifications indicate data changes
 */

import { useEffect, useCallback } from 'react';
import { notificationService } from '../services/notificationService';

export const useAutoRefresh = (refreshCallback: () => void, dependencies: any[] = []) => {
  const memoizedRefresh = useCallback(refreshCallback, [refreshCallback, ...dependencies]);

  useEffect(() => {
    // Subscribe to refresh events from notification service
    const unsubscribe = notificationService.subscribeToRefresh(() => {
      // Add a small delay to ensure backend has processed the change
      setTimeout(() => {
        memoizedRefresh();
      }, 500);
    });

    return unsubscribe;
  }, [memoizedRefresh]);

  // Also refresh on interval for real-time updates (every 30 seconds)
  useEffect(() => {
    const interval = setInterval(() => {
      memoizedRefresh();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [memoizedRefresh]);
};

export default useAutoRefresh;
