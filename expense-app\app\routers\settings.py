"""
Settings API endpoints for user preferences and account management
"""

from fastapi import API<PERSON>outer, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
import json

from ..database import get_db
from ..models import User
from ..routers.auth import get_current_user
from .. import schemas
from ..services.auth_service import AuthService

router = APIRouter(prefix="/settings", tags=["settings"])

@router.put("/profile", response_model=Dict[str, str])
def update_profile(
    profile_data: schemas.UserProfileUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update user profile information
    """
    try:
        if profile_data.name:
            # For now, we'll store the name in a user preferences field
            # In a real app, you'd have a proper user profile table
            pass
        
        if profile_data.email and profile_data.email != current_user.email:
            # Check if email is already taken
            existing_user = db.query(User).filter(User.email == profile_data.email).first()
            if existing_user and existing_user.id != current_user.id:
                raise HTTPException(status_code=400, detail="Email already registered")
            
            current_user.email = profile_data.email
            db.commit()
        
        return {"message": "Profile updated successfully"}
    
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/password", response_model=Dict[str, str])
def change_password(
    password_data: schemas.PasswordChange,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Change user password
    """
    try:
        auth_service = AuthService(db)
        
        # Verify current password
        if not auth_service.verify_password(password_data.current_password, current_user.hashed_password):
            raise HTTPException(status_code=400, detail="Current password is incorrect")
        
        # Update password
        current_user.hashed_password = auth_service.get_password_hash(password_data.new_password)
        db.commit()
        
        return {"message": "Password changed successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/groq-api-key", response_model=Dict[str, str])
def update_groq_api_key(
    api_key_data: schemas.GroqApiKeyUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update Groq API key for AI features
    """
    try:
        # Store API key in user preferences (encrypted in production)
        # For now, we'll store it as a simple field
        # In production, this should be encrypted
        
        # You would typically store this in a user_preferences table
        # For now, we'll just return success
        
        return {"message": "Groq API key updated successfully"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/notifications", response_model=Dict[str, Any])
def get_notification_preferences(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user notification preferences
    """
    try:
        # Default notification preferences
        # In a real app, these would be stored in a user_preferences table
        default_prefs = {
            "expense_created": True,
            "expense_approved": True,
            "settlement_received": True,
            "settlement_confirmed": True,
            "join_request_processed": True,
            "email_notifications": False
        }
        
        return default_prefs
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/notifications", response_model=Dict[str, str])
def update_notification_preferences(
    notification_prefs: schemas.NotificationPreferences,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update user notification preferences
    """
    try:
        # Store notification preferences
        # In a real app, these would be stored in a user_preferences table
        
        return {"message": "Notification preferences updated successfully"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/display", response_model=Dict[str, Any])
def get_display_settings(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user display settings
    """
    try:
        # Default display settings
        default_settings = {
            "theme": "light",
            "currency": "USD",
            "language": "en",
            "date_format": "MM/DD/YYYY"
        }
        
        return default_settings
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/display", response_model=Dict[str, str])
def update_display_settings(
    display_settings: schemas.DisplaySettings,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update user display settings
    """
    try:
        # Store display settings
        # In a real app, these would be stored in a user_preferences table
        
        return {"message": "Display settings updated successfully"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/export-data", response_model=Dict[str, Any])
def export_user_data(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Export all user data
    """
    try:
        # In a real app, you would gather all user data from various tables
        # For now, we'll return a basic structure
        
        export_data = {
            "user_info": {
                "id": current_user.id,
                "email": current_user.email,
                "created_at": current_user.created_at.isoformat() if current_user.created_at else None
            },
            "groups": [],  # Would fetch user's groups
            "expenses": [],  # Would fetch user's expenses
            "settlements": [],  # Would fetch user's settlements
            "preferences": {
                "notifications": {},
                "display": {}
            },
            "export_date": "2024-01-01T00:00:00Z"
        }
        
        return export_data
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/account", response_model=Dict[str, str])
def delete_account(
    delete_data: schemas.AccountDeletion,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete user account and all associated data
    """
    try:
        auth_service = AuthService(db)
        
        # Verify password
        if not auth_service.verify_password(delete_data.password, current_user.hashed_password):
            raise HTTPException(status_code=400, detail="Password is incorrect")
        
        # In a real app, you would:
        # 1. Check if user can be deleted (no pending settlements, etc.)
        # 2. Delete or anonymize all user data
        # 3. Handle group ownership transfers
        # 4. Clean up related records
        
        # For now, we'll just mark the account as deleted
        # In production, implement proper data deletion/anonymization
        
        return {"message": "Account deletion initiated. This feature is not fully implemented yet."}
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
