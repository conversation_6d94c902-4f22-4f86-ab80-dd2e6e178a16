#!/usr/bin/env python3
"""
Simple test to verify settlement response schema fix
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_settlement_response_fix():
    """Test that settlement creation returns proper response schema"""
    
    print("🧪 Testing Settlement Response Schema Fix")
    print("=" * 50)
    
    # Step 1: Setup a test user
    print("1. Setting up test user...")
    
    user_data = {"email": "<EMAIL>", "password": "test123", "groq_api_key": "test_key"}
    
    # Register (ignore if already exists)
    requests.post(f"{BASE_URL}/auth/register", json=user_data)
    
    # Login
    login_data = {"email": user_data["email"], "password": user_data["password"]}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code}")
        return False
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get user ID
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    user_id = response.json()["id"]
    
    print(f"✅ Test user ready (ID: {user_id})")
    
    # Step 2: Test settlement creation with proper response schema
    print("\n2. Testing settlement creation response schema...")
    
    settlement_data = {
        "target_user_id": user_id,  # Self-settlement for testing
        "amount": 25.00
    }
    
    response = requests.post(f"{BASE_URL}/expenses/settle", json=settlement_data, headers=headers)
    
    print(f"Settlement creation status: {response.status_code}")
    
    if response.status_code == 200:
        settlement_response = response.json()
        print("✅ Settlement created successfully!")
        print(f"\nResponse structure:")
        print(json.dumps(settlement_response, indent=2))
        
        # Check required fields from SettlementSummary schema
        required_fields = ["total_paid", "target_user_email", "settlements", "message"]
        
        print(f"\n3. Validating response schema...")
        all_present = True
        
        for field in required_fields:
            if field in settlement_response:
                print(f"✅ {field}: {type(settlement_response[field]).__name__}")
            else:
                print(f"❌ {field}: MISSING")
                all_present = False
        
        if all_present:
            print("\n🎉 ALL REQUIRED FIELDS PRESENT!")
            print("✅ Settlement response schema is now correct")
            print("✅ No more 500 Internal Server Errors")
            print("✅ Frontend settlement workflow will now work")
            return True
        else:
            print("\n❌ Some required fields are missing")
            return False
            
    elif response.status_code == 500:
        print("❌ Still getting 500 Internal Server Error")
        print("This indicates the response validation fix didn't work")
        print(f"Error: {response.text}")
        return False
    else:
        print(f"❌ Settlement creation failed with status: {response.status_code}")
        print(f"Error: {response.text}")
        return False

if __name__ == "__main__":
    print("🔧 Settlement Response Schema Fix Test")
    print("=" * 60)
    print("This test verifies that the settlement endpoint now returns")
    print("the correct response schema without 500 errors.")
    print()
    
    success = test_settlement_response_fix()
    
    if success:
        print("\n🎉 SETTLEMENT RESPONSE FIX SUCCESSFUL!")
        print("The settlement endpoint now:")
        print("  ✅ Returns proper SettlementSummary schema")
        print("  ✅ Includes all required fields")
        print("  ✅ No longer causes 500 Internal Server Errors")
        print("  ✅ Frontend settlement workflow will work correctly")
    else:
        print("\n❌ SETTLEMENT RESPONSE FIX FAILED")
        print("The settlement endpoint still has issues")
