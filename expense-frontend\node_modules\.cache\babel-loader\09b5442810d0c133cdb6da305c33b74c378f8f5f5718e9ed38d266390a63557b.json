{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\components\\\\GroupManagement\\\\GroupManagementModal.tsx\",\n  _s = $RefreshSig$();\n/**\n * Comprehensive Group Management Modal with owner controls\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { X, Crown, UserMinus, UserCheck, UserX, Settings, LogOut, AlertTriangle, Edit3, Save, Users } from 'lucide-react';\nimport { groupManagementAPI } from '../../services/api';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GroupManagementModal = ({\n  isOpen,\n  onClose,\n  groupId,\n  groupName,\n  isOwner,\n  onGroupUpdated\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('details');\n  const [groupDetails, setGroupDetails] = useState(null);\n  const [joinRequests, setJoinRequests] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [processing, setProcessing] = useState([]);\n\n  // Edit states\n  const [isEditing, setIsEditing] = useState(false);\n  const [editName, setEditName] = useState('');\n  const [editDescription, setEditDescription] = useState('');\n\n  // Leave group state\n  const [canLeave, setCanLeave] = useState(null);\n\n  // Transfer ownership state\n  const [showTransferOwnership, setShowTransferOwnership] = useState(false);\n  const [selectedNewOwner, setSelectedNewOwner] = useState(null);\n  const [transferConfirmation, setTransferConfirmation] = useState('');\n\n  // Confirmation dialog state\n  const [confirmDialog, setConfirmDialog] = useState({\n    isOpen: false,\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n  useEffect(() => {\n    if (isOpen) {\n      loadGroupDetails();\n      if (isOwner) {\n        loadJoinRequests();\n      }\n      checkCanLeave();\n    }\n  }, [isOpen, groupId]);\n  const loadGroupDetails = async () => {\n    try {\n      setLoading(true);\n      const response = await groupManagementAPI.getGroupDetails(groupId);\n      setGroupDetails(response.data);\n      setEditName(response.data.name);\n      setEditDescription(response.data.description || '');\n    } catch (error) {\n      toast.error('Failed to load group details');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadJoinRequests = async () => {\n    try {\n      const response = await groupManagementAPI.getPendingJoinRequests(groupId);\n      setJoinRequests(response.data);\n    } catch (error) {\n      console.error('Failed to load join requests:', error);\n    }\n  };\n  const checkCanLeave = async () => {\n    try {\n      const response = await groupManagementAPI.checkCanLeave(groupId);\n      setCanLeave(response.data);\n    } catch (error) {\n      console.error('Failed to check leave status:', error);\n    }\n  };\n  const handleUpdateGroup = async () => {\n    if (!editName.trim()) {\n      toast.error('Group name is required');\n      return;\n    }\n    try {\n      await groupManagementAPI.updateGroup(groupId, {\n        name: editName,\n        description: editDescription\n      });\n      setIsEditing(false);\n      toast.success('Group updated successfully');\n      loadGroupDetails();\n      onGroupUpdated();\n    } catch (error) {\n      toast.error('Failed to update group');\n    }\n  };\n  const handleRemoveMember = async (userId, userEmail) => {\n    if (!confirm(`Are you sure you want to remove ${userEmail} from this group?`)) {\n      return;\n    }\n    try {\n      setProcessing([...processing, userId]);\n      await groupManagementAPI.removeMember(groupId, userId);\n      toast.success(`${userEmail} removed from group`);\n      loadGroupDetails();\n      onGroupUpdated();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Failed to remove member');\n    } finally {\n      setProcessing(processing.filter(id => id !== userId));\n    }\n  };\n  const handleLeaveGroup = async () => {\n    if (!(canLeave !== null && canLeave !== void 0 && canLeave.can_leave)) {\n      toast.error((canLeave === null || canLeave === void 0 ? void 0 : canLeave.message) || 'Cannot leave group');\n      return;\n    }\n    if (!confirm('Are you sure you want to leave this group? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      await groupManagementAPI.leaveGroup(groupId);\n      toast.success('You have left the group');\n      onClose();\n      onGroupUpdated();\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Failed to leave group');\n    }\n  };\n  const handleProcessJoinRequest = async (requestId, approved, userEmail) => {\n    try {\n      setProcessing([...processing, requestId]);\n      await groupManagementAPI.processJoinRequest(requestId, approved);\n      toast.success(`Join request ${approved ? 'approved' : 'rejected'} for ${userEmail}`);\n      loadJoinRequests();\n      if (approved) {\n        loadGroupDetails(); // Refresh member list\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      toast.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Failed to process join request');\n    } finally {\n      setProcessing(processing.filter(id => id !== requestId));\n    }\n  };\n  const handleTransferOwnership = async () => {\n    if (!selectedNewOwner) {\n      toast.error('Please select a new owner');\n      return;\n    }\n    if (transferConfirmation !== 'TRANSFER') {\n      toast.error('Please type TRANSFER to confirm');\n      return;\n    }\n    const newOwner = groupDetails === null || groupDetails === void 0 ? void 0 : groupDetails.members.find(m => m.user_id === selectedNewOwner);\n    if (!newOwner) {\n      toast.error('Selected user not found');\n      return;\n    }\n    if (!confirm(`Are you sure you want to transfer ownership to ${newOwner.email}? This action cannot be undone and you will lose all owner privileges.`)) {\n      return;\n    }\n    try {\n      setSaving(true);\n      await groupManagementAPI.transferOwnership(groupId, selectedNewOwner);\n      toast.success(`Ownership transferred to ${newOwner.email}`);\n\n      // Reset transfer state\n      setShowTransferOwnership(false);\n      setSelectedNewOwner(null);\n      setTransferConfirmation('');\n\n      // Refresh group details and close modal since user is no longer owner\n      loadGroupDetails();\n      onGroupUpdated();\n\n      // Close modal after a short delay to show success message\n      setTimeout(() => {\n        onClose();\n      }, 1500);\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      toast.error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || 'Failed to transfer ownership');\n    } finally {\n      setSaving(false);\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 bg-primary-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(Users, {\n              className: \"w-6 h-6 text-primary-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: groupName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Group Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex space-x-8 px-6\",\n          children: ['details', 'members', ...(isOwner ? ['requests'] : []), 'settings'].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab),\n            className: `py-4 px-1 border-b-2 font-medium text-sm capitalize ${activeTab === tab ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n            children: [tab === 'requests' && joinRequests.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full\",\n              children: joinRequests.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this), tab]\n          }, tab, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 max-h-[60vh] overflow-y-auto\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [activeTab === 'details' && groupDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-900\",\n                  children: \"Group Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this), isOwner && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setIsEditing(!isEditing),\n                  className: \"btn-ghost text-sm\",\n                  children: [isEditing ? /*#__PURE__*/_jsxDEV(X, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 40\n                  }, this) : /*#__PURE__*/_jsxDEV(Edit3, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 68\n                  }, this), isEditing ? 'Cancel' : 'Edit']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Group Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: editName,\n                    onChange: e => setEditName(e.target.value),\n                    className: \"input-field\",\n                    placeholder: \"Enter group name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: editDescription,\n                    onChange: e => setEditDescription(e.target.value),\n                    className: \"input-field\",\n                    rows: 3,\n                    placeholder: \"Enter group description (optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleUpdateGroup,\n                  className: \"btn-primary\",\n                  children: [/*#__PURE__*/_jsxDEV(Save, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 27\n                  }, this), \"Save Changes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Name:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: groupDetails.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Members:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: groupDetails.member_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Owner:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Crown, {\n                      className: \"w-4 h-4 text-yellow-500 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 29\n                    }, this), groupDetails.creator_email]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Created:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: new Date(groupDetails.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 25\n                }, this), groupDetails.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Description:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-gray-900\",\n                    children: groupDetails.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 17\n          }, this), activeTab === 'members' && groupDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Group Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: groupDetails.members.map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-primary-600\",\n                      children: member.email.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-900\",\n                      children: member.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [member.user_id === groupDetails.creator_id && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                        children: [/*#__PURE__*/_jsxDEV(Crown, {\n                          className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 427,\n                          columnNumber: 35\n                        }, this), \"Owner\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-sm ${member.balance >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                        children: [\"Balance: $\", Math.abs(member.balance).toFixed(2), \" \", member.balance >= 0 ? 'owed to them' : 'they owe']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 25\n                }, this), isOwner && member.user_id !== groupDetails.creator_id && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleRemoveMember(member.user_id, member.email),\n                  disabled: processing.includes(member.user_id),\n                  className: \"btn-ghost text-red-600 hover:bg-red-50\",\n                  children: /*#__PURE__*/_jsxDEV(UserMinus, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 27\n                }, this)]\n              }, member.user_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 17\n          }, this), activeTab === 'requests' && isOwner && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Pending Join Requests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 19\n            }, this), joinRequests.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8 text-gray-500\",\n              children: \"No pending join requests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: joinRequests.map(request => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 bg-gray-50 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-900\",\n                      children: request.user_email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: [\"Requested \", new Date(request.created_at).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 31\n                    }, this), request.message && /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-700 mt-1\",\n                      children: [\"\\\"\", request.message, \"\\\"\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleProcessJoinRequest(request.request_id, true, request.user_email),\n                      disabled: processing.includes(request.request_id),\n                      className: \"btn-primary text-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(UserCheck, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 33\n                      }, this), \"Approve\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleProcessJoinRequest(request.request_id, false, request.user_email),\n                      disabled: processing.includes(request.request_id),\n                      className: \"btn-secondary text-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(UserX, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 489,\n                        columnNumber: 33\n                      }, this), \"Reject\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 27\n                }, this)\n              }, request.request_id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 17\n          }, this), activeTab === 'settings' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Group Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 19\n            }, this), !isOwner && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                  className: \"w-5 h-5 text-red-600 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-red-900\",\n                    children: \"Leave Group\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-red-700 mt-1\",\n                    children: (canLeave === null || canLeave === void 0 ? void 0 : canLeave.message) || 'Loading...'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 27\n                  }, this), canLeave && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleLeaveGroup,\n                    disabled: !canLeave.can_leave,\n                    className: `mt-3 ${canLeave.can_leave ? 'btn-danger' : 'bg-gray-300 text-gray-500 cursor-not-allowed px-4 py-2 rounded-lg'}`,\n                    children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                      className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 31\n                    }, this), \"Leave Group\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 21\n            }, this), isOwner && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Crown, {\n                    className: \"w-5 h-5 text-yellow-600 mt-0.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-yellow-900\",\n                      children: \"Owner Controls\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-yellow-700 mt-1\",\n                      children: \"As the group owner, you can manage members, approve join requests, and transfer ownership.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 mb-2\",\n                  children: \"Transfer Ownership\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-3\",\n                  children: \"Transfer ownership to another group member. This action cannot be undone.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 25\n                }, this), !showTransferOwnership ? /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn-secondary\",\n                  onClick: () => setShowTransferOwnership(true),\n                  children: [/*#__PURE__*/_jsxDEV(Settings, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 29\n                  }, this), \"Transfer Ownership\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Select New Owner\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      value: selectedNewOwner || '',\n                      onChange: e => setSelectedNewOwner(e.target.value ? parseInt(e.target.value) : null),\n                      className: \"input-field\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Choose a member...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 575,\n                        columnNumber: 33\n                      }, this), groupDetails === null || groupDetails === void 0 ? void 0 : groupDetails.members.filter(member => member.user_id !== groupDetails.creator_id).map(member => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: member.user_id,\n                        children: member.email\n                      }, member.user_id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 579,\n                        columnNumber: 37\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Type \\\"TRANSFER\\\" to confirm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: transferConfirmation,\n                      onChange: e => setTransferConfirmation(e.target.value),\n                      className: \"input-field\",\n                      placeholder: \"Type TRANSFER\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                        className: \"w-4 h-4 text-red-600 mt-0.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 602,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-red-700\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-medium\",\n                          children: \"Warning:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 604,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: \"You will lose all owner privileges and cannot undo this action.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 605,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 603,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleTransferOwnership,\n                      disabled: saving || !selectedNewOwner || transferConfirmation !== 'TRANSFER',\n                      className: `${saving || !selectedNewOwner || transferConfirmation !== 'TRANSFER' ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700 text-white'} px-4 py-2 rounded-lg font-medium flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(Settings, {\n                        className: \"w-4 h-4 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 620,\n                        columnNumber: 33\n                      }, this), saving ? 'Transferring...' : 'Transfer Ownership']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setShowTransferOwnership(false);\n                        setSelectedNewOwner(null);\n                        setTransferConfirmation('');\n                      },\n                      className: \"btn-secondary\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 270,\n    columnNumber: 5\n  }, this);\n};\n_s(GroupManagementModal, \"NOCx7clcbxTNv4L+IldJgOdk9js=\", false, function () {\n  return [useAuth];\n});\n_c = GroupManagementModal;\nexport default GroupManagementModal;\nvar _c;\n$RefreshReg$(_c, \"GroupManagementModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "Crown", "UserMinus", "UserCheck", "UserX", "Settings", "LogOut", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Edit3", "Save", "Users", "groupManagementAPI", "useAuth", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GroupManagementModal", "isOpen", "onClose", "groupId", "groupName", "isOwner", "onGroupUpdated", "_s", "user", "activeTab", "setActiveTab", "groupDetails", "setGroupDetails", "joinRequests", "setJoinRequests", "loading", "setLoading", "saving", "setSaving", "processing", "setProcessing", "isEditing", "setIsEditing", "editName", "setEditName", "editDescription", "setEditDescription", "canLeave", "setCanLeave", "showTransferOwnership", "setShowTransferOwnership", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedNewOwner", "transferConfirmation", "setTransferConfirmation", "confirmDialog", "setConfirmDialog", "title", "message", "onConfirm", "loadGroupDetails", "loadJoinRequests", "checkCanLeave", "response", "getGroupDetails", "data", "name", "description", "error", "getPendingJoinRequests", "console", "handleUpdateGroup", "trim", "updateGroup", "success", "handleRemoveMember", "userId", "userEmail", "confirm", "removeMember", "_error$response", "_error$response$data", "detail", "filter", "id", "handleLeaveGroup", "can_leave", "leaveGroup", "_error$response2", "_error$response2$data", "handleProcessJoinRequest", "requestId", "approved", "processJoinRequest", "_error$response3", "_error$response3$data", "handleTransferOwnership", "new<PERSON>wner", "members", "find", "m", "user_id", "email", "transferOwnership", "setTimeout", "_error$response4", "_error$response4$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "tab", "length", "type", "value", "onChange", "e", "target", "placeholder", "rows", "member_count", "creator_email", "Date", "created_at", "toLocaleDateString", "member", "char<PERSON>t", "toUpperCase", "creator_id", "balance", "Math", "abs", "toFixed", "disabled", "includes", "request", "user_email", "request_id", "parseInt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/components/GroupManagement/GroupManagementModal.tsx"], "sourcesContent": ["/**\n * Comprehensive Group Management Modal with owner controls\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  X, \n  Crown, \n  UserMinus, \n  UserCheck, \n  UserX, \n  Settings, \n  LogOut,\n  AlertTriangle,\n  Edit3,\n  Save,\n  Users\n} from 'lucide-react';\nimport { groupManagementAPI } from '../../services/api';\nimport { useAuth } from '../../contexts/AuthContext';\nimport ConfirmationDialog from '../UI/ConfirmationDialog';\nimport toast from 'react-hot-toast';\n\ninterface GroupManagementModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  groupId: number;\n  groupName: string;\n  isOwner: boolean;\n  onGroupUpdated: () => void;\n}\n\ninterface GroupDetails {\n  id: number;\n  name: string;\n  description: string;\n  creator_id: number;\n  creator_email: string;\n  created_at: string;\n  member_count: number;\n  members: Array<{\n    user_id: number;\n    email: string;\n    balance: number;\n  }>;\n  is_owner: boolean;\n}\n\ninterface JoinRequest {\n  request_id: number;\n  group_id: number;\n  group_name: string;\n  user_id: number;\n  user_email: string;\n  message: string;\n  created_at: string;\n}\n\nconst GroupManagementModal: React.FC<GroupManagementModalProps> = ({\n  isOpen,\n  onClose,\n  groupId,\n  groupName,\n  isOwner,\n  onGroupUpdated\n}) => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'details' | 'members' | 'requests' | 'settings'>('details');\n  const [groupDetails, setGroupDetails] = useState<GroupDetails | null>(null);\n  const [joinRequests, setJoinRequests] = useState<JoinRequest[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [processing, setProcessing] = useState<number[]>([]);\n  \n  // Edit states\n  const [isEditing, setIsEditing] = useState(false);\n  const [editName, setEditName] = useState('');\n  const [editDescription, setEditDescription] = useState('');\n  \n  // Leave group state\n  const [canLeave, setCanLeave] = useState<{ can_leave: boolean; balance: number; message: string } | null>(null);\n\n  // Transfer ownership state\n  const [showTransferOwnership, setShowTransferOwnership] = useState(false);\n  const [selectedNewOwner, setSelectedNewOwner] = useState<number | null>(null);\n  const [transferConfirmation, setTransferConfirmation] = useState('');\n\n  // Confirmation dialog state\n  const [confirmDialog, setConfirmDialog] = useState<{\n    isOpen: boolean;\n    title: string;\n    message: string;\n    onConfirm: () => void;\n    type?: 'danger' | 'warning' | 'info';\n    confirmText?: string;\n  }>({\n    isOpen: false,\n    title: '',\n    message: '',\n    onConfirm: () => {},\n  });\n\n  useEffect(() => {\n    if (isOpen) {\n      loadGroupDetails();\n      if (isOwner) {\n        loadJoinRequests();\n      }\n      checkCanLeave();\n    }\n  }, [isOpen, groupId]);\n\n  const loadGroupDetails = async () => {\n    try {\n      setLoading(true);\n      const response = await groupManagementAPI.getGroupDetails(groupId);\n      setGroupDetails(response.data);\n      setEditName(response.data.name);\n      setEditDescription(response.data.description || '');\n    } catch (error) {\n      toast.error('Failed to load group details');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadJoinRequests = async () => {\n    try {\n      const response = await groupManagementAPI.getPendingJoinRequests(groupId);\n      setJoinRequests(response.data);\n    } catch (error) {\n      console.error('Failed to load join requests:', error);\n    }\n  };\n\n  const checkCanLeave = async () => {\n    try {\n      const response = await groupManagementAPI.checkCanLeave(groupId);\n      setCanLeave(response.data);\n    } catch (error) {\n      console.error('Failed to check leave status:', error);\n    }\n  };\n\n  const handleUpdateGroup = async () => {\n    if (!editName.trim()) {\n      toast.error('Group name is required');\n      return;\n    }\n\n    try {\n      await groupManagementAPI.updateGroup(groupId, {\n        name: editName,\n        description: editDescription\n      });\n      \n      setIsEditing(false);\n      toast.success('Group updated successfully');\n      loadGroupDetails();\n      onGroupUpdated();\n    } catch (error) {\n      toast.error('Failed to update group');\n    }\n  };\n\n  const handleRemoveMember = async (userId: number, userEmail: string) => {\n    if (!confirm(`Are you sure you want to remove ${userEmail} from this group?`)) {\n      return;\n    }\n\n    try {\n      setProcessing([...processing, userId]);\n      await groupManagementAPI.removeMember(groupId, userId);\n      toast.success(`${userEmail} removed from group`);\n      loadGroupDetails();\n      onGroupUpdated();\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to remove member');\n    } finally {\n      setProcessing(processing.filter(id => id !== userId));\n    }\n  };\n\n  const handleLeaveGroup = async () => {\n    if (!canLeave?.can_leave) {\n      toast.error(canLeave?.message || 'Cannot leave group');\n      return;\n    }\n\n    if (!confirm('Are you sure you want to leave this group? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      await groupManagementAPI.leaveGroup(groupId);\n      toast.success('You have left the group');\n      onClose();\n      onGroupUpdated();\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to leave group');\n    }\n  };\n\n  const handleProcessJoinRequest = async (requestId: number, approved: boolean, userEmail: string) => {\n    try {\n      setProcessing([...processing, requestId]);\n      await groupManagementAPI.processJoinRequest(requestId, approved);\n      toast.success(`Join request ${approved ? 'approved' : 'rejected'} for ${userEmail}`);\n      loadJoinRequests();\n      if (approved) {\n        loadGroupDetails(); // Refresh member list\n      }\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to process join request');\n    } finally {\n      setProcessing(processing.filter(id => id !== requestId));\n    }\n  };\n\n  const handleTransferOwnership = async () => {\n    if (!selectedNewOwner) {\n      toast.error('Please select a new owner');\n      return;\n    }\n\n    if (transferConfirmation !== 'TRANSFER') {\n      toast.error('Please type TRANSFER to confirm');\n      return;\n    }\n\n    const newOwner = groupDetails?.members.find(m => m.user_id === selectedNewOwner);\n    if (!newOwner) {\n      toast.error('Selected user not found');\n      return;\n    }\n\n    if (!confirm(`Are you sure you want to transfer ownership to ${newOwner.email}? This action cannot be undone and you will lose all owner privileges.`)) {\n      return;\n    }\n\n    try {\n      setSaving(true);\n      await groupManagementAPI.transferOwnership(groupId, selectedNewOwner);\n      toast.success(`Ownership transferred to ${newOwner.email}`);\n\n      // Reset transfer state\n      setShowTransferOwnership(false);\n      setSelectedNewOwner(null);\n      setTransferConfirmation('');\n\n      // Refresh group details and close modal since user is no longer owner\n      loadGroupDetails();\n      onGroupUpdated();\n\n      // Close modal after a short delay to show success message\n      setTimeout(() => {\n        onClose();\n      }, 1500);\n\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to transfer ownership');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-2 bg-primary-100 rounded-lg\">\n              <Users className=\"w-6 h-6 text-primary-600\" />\n            </div>\n            <div>\n              <h2 className=\"text-xl font-semibold text-gray-900\">{groupName}</h2>\n              <p className=\"text-sm text-gray-600\">Group Management</p>\n            </div>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {['details', 'members', ...(isOwner ? ['requests'] : []), 'settings'].map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab as any)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${\n                  activeTab === tab\n                    ? 'border-primary-500 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                {tab === 'requests' && joinRequests.length > 0 && (\n                  <span className=\"ml-2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full\">\n                    {joinRequests.length}\n                  </span>\n                )}\n                {tab}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {loading ? (\n            <div className=\"flex items-center justify-center py-12\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n            </div>\n          ) : (\n            <>\n              {/* Details Tab */}\n              {activeTab === 'details' && groupDetails && (\n                <div className=\"space-y-6\">\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"font-medium text-gray-900\">Group Information</h3>\n                      {isOwner && (\n                        <button\n                          onClick={() => setIsEditing(!isEditing)}\n                          className=\"btn-ghost text-sm\"\n                        >\n                          {isEditing ? <X className=\"w-4 h-4\" /> : <Edit3 className=\"w-4 h-4\" />}\n                          {isEditing ? 'Cancel' : 'Edit'}\n                        </button>\n                      )}\n                    </div>\n                    \n                    {isEditing ? (\n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Group Name\n                          </label>\n                          <input\n                            type=\"text\"\n                            value={editName}\n                            onChange={(e) => setEditName(e.target.value)}\n                            className=\"input-field\"\n                            placeholder=\"Enter group name\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Description\n                          </label>\n                          <textarea\n                            value={editDescription}\n                            onChange={(e) => setEditDescription(e.target.value)}\n                            className=\"input-field\"\n                            rows={3}\n                            placeholder=\"Enter group description (optional)\"\n                          />\n                        </div>\n                        <button\n                          onClick={handleUpdateGroup}\n                          className=\"btn-primary\"\n                        >\n                          <Save className=\"w-4 h-4 mr-2\" />\n                          Save Changes\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                        <div>\n                          <span className=\"text-gray-600\">Name:</span>\n                          <span className=\"ml-2 font-medium\">{groupDetails.name}</span>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">Members:</span>\n                          <span className=\"ml-2 font-medium\">{groupDetails.member_count}</span>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">Owner:</span>\n                          <span className=\"ml-2 font-medium flex items-center\">\n                            <Crown className=\"w-4 h-4 text-yellow-500 mr-1\" />\n                            {groupDetails.creator_email}\n                          </span>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">Created:</span>\n                          <span className=\"ml-2 font-medium\">\n                            {new Date(groupDetails.created_at).toLocaleDateString()}\n                          </span>\n                        </div>\n                        {groupDetails.description && (\n                          <div className=\"col-span-2\">\n                            <span className=\"text-gray-600\">Description:</span>\n                            <p className=\"mt-1 text-gray-900\">{groupDetails.description}</p>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Members Tab */}\n              {activeTab === 'members' && groupDetails && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"font-medium text-gray-900\">Group Members</h3>\n                  <div className=\"space-y-3\">\n                    {groupDetails.members.map((member) => (\n                      <div key={member.user_id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-medium text-primary-600\">\n                              {member.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div>\n                            <p className=\"font-medium text-gray-900\">{member.email}</p>\n                            <div className=\"flex items-center space-x-2\">\n                              {member.user_id === groupDetails.creator_id && (\n                                <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                                  <Crown className=\"w-3 h-3 mr-1\" />\n                                  Owner\n                                </span>\n                              )}\n                              <span className={`text-sm ${member.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                                Balance: ${Math.abs(member.balance).toFixed(2)} {member.balance >= 0 ? 'owed to them' : 'they owe'}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                        \n                        {isOwner && member.user_id !== groupDetails.creator_id && (\n                          <button\n                            onClick={() => handleRemoveMember(member.user_id, member.email)}\n                            disabled={processing.includes(member.user_id)}\n                            className=\"btn-ghost text-red-600 hover:bg-red-50\"\n                          >\n                            <UserMinus className=\"w-4 h-4\" />\n                          </button>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Join Requests Tab (Owner Only) */}\n              {activeTab === 'requests' && isOwner && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"font-medium text-gray-900\">Pending Join Requests</h3>\n                  {joinRequests.length === 0 ? (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      No pending join requests\n                    </div>\n                  ) : (\n                    <div className=\"space-y-3\">\n                      {joinRequests.map((request) => (\n                        <div key={request.request_id} className=\"p-4 bg-gray-50 rounded-lg\">\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <p className=\"font-medium text-gray-900\">{request.user_email}</p>\n                              <p className=\"text-sm text-gray-600\">\n                                Requested {new Date(request.created_at).toLocaleDateString()}\n                              </p>\n                              {request.message && (\n                                <p className=\"text-sm text-gray-700 mt-1\">\"{request.message}\"</p>\n                              )}\n                            </div>\n                            <div className=\"flex space-x-2\">\n                              <button\n                                onClick={() => handleProcessJoinRequest(request.request_id, true, request.user_email)}\n                                disabled={processing.includes(request.request_id)}\n                                className=\"btn-primary text-sm\"\n                              >\n                                <UserCheck className=\"w-4 h-4 mr-1\" />\n                                Approve\n                              </button>\n                              <button\n                                onClick={() => handleProcessJoinRequest(request.request_id, false, request.user_email)}\n                                disabled={processing.includes(request.request_id)}\n                                className=\"btn-secondary text-sm\"\n                              >\n                                <UserX className=\"w-4 h-4 mr-1\" />\n                                Reject\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Settings Tab */}\n              {activeTab === 'settings' && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"font-medium text-gray-900\">Group Settings</h3>\n                  \n                  {/* Leave Group */}\n                  {!isOwner && (\n                    <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                      <div className=\"flex items-start space-x-3\">\n                        <AlertTriangle className=\"w-5 h-5 text-red-600 mt-0.5\" />\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-red-900\">Leave Group</h4>\n                          <p className=\"text-sm text-red-700 mt-1\">\n                            {canLeave?.message || 'Loading...'}\n                          </p>\n                          {canLeave && (\n                            <button\n                              onClick={handleLeaveGroup}\n                              disabled={!canLeave.can_leave}\n                              className={`mt-3 ${\n                                canLeave.can_leave \n                                  ? 'btn-danger' \n                                  : 'bg-gray-300 text-gray-500 cursor-not-allowed px-4 py-2 rounded-lg'\n                              }`}\n                            >\n                              <LogOut className=\"w-4 h-4 mr-2\" />\n                              Leave Group\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Owner Settings */}\n                  {isOwner && (\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                        <div className=\"flex items-start space-x-3\">\n                          <Crown className=\"w-5 h-5 text-yellow-600 mt-0.5\" />\n                          <div>\n                            <h4 className=\"font-medium text-yellow-900\">Owner Controls</h4>\n                            <p className=\"text-sm text-yellow-700 mt-1\">\n                              As the group owner, you can manage members, approve join requests, and transfer ownership.\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"bg-gray-50 rounded-lg p-4\">\n                        <h4 className=\"font-medium text-gray-900 mb-2\">Transfer Ownership</h4>\n                        <p className=\"text-sm text-gray-600 mb-3\">\n                          Transfer ownership to another group member. This action cannot be undone.\n                        </p>\n\n                        {!showTransferOwnership ? (\n                          <button\n                            className=\"btn-secondary\"\n                            onClick={() => setShowTransferOwnership(true)}\n                          >\n                            <Settings className=\"w-4 h-4 mr-2\" />\n                            Transfer Ownership\n                          </button>\n                        ) : (\n                          <div className=\"space-y-4\">\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Select New Owner\n                              </label>\n                              <select\n                                value={selectedNewOwner || ''}\n                                onChange={(e) => setSelectedNewOwner(e.target.value ? parseInt(e.target.value) : null)}\n                                className=\"input-field\"\n                              >\n                                <option value=\"\">Choose a member...</option>\n                                {groupDetails?.members\n                                  .filter(member => member.user_id !== groupDetails.creator_id)\n                                  .map(member => (\n                                    <option key={member.user_id} value={member.user_id}>\n                                      {member.email}\n                                    </option>\n                                  ))\n                                }\n                              </select>\n                            </div>\n\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Type \"TRANSFER\" to confirm\n                              </label>\n                              <input\n                                type=\"text\"\n                                value={transferConfirmation}\n                                onChange={(e) => setTransferConfirmation(e.target.value)}\n                                className=\"input-field\"\n                                placeholder=\"Type TRANSFER\"\n                              />\n                            </div>\n\n                            <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n                              <div className=\"flex items-start space-x-2\">\n                                <AlertTriangle className=\"w-4 h-4 text-red-600 mt-0.5\" />\n                                <div className=\"text-sm text-red-700\">\n                                  <p className=\"font-medium\">Warning:</p>\n                                  <p>You will lose all owner privileges and cannot undo this action.</p>\n                                </div>\n                              </div>\n                            </div>\n\n                            <div className=\"flex space-x-3\">\n                              <button\n                                onClick={handleTransferOwnership}\n                                disabled={saving || !selectedNewOwner || transferConfirmation !== 'TRANSFER'}\n                                className={`${\n                                  saving || !selectedNewOwner || transferConfirmation !== 'TRANSFER'\n                                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                                    : 'bg-red-600 hover:bg-red-700 text-white'\n                                } px-4 py-2 rounded-lg font-medium flex items-center`}\n                              >\n                                <Settings className=\"w-4 h-4 mr-2\" />\n                                {saving ? 'Transferring...' : 'Transfer Ownership'}\n                              </button>\n                              <button\n                                onClick={() => {\n                                  setShowTransferOwnership(false);\n                                  setSelectedNewOwner(null);\n                                  setTransferConfirmation('');\n                                }}\n                                className=\"btn-secondary\"\n                              >\n                                Cancel\n                              </button>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GroupManagementModal;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,CAAC,EACDC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,aAAa,EACbC,KAAK,EACLC,IAAI,EACJC,KAAK,QACA,cAAc;AACrB,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqCpC,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC,SAAS;EACTC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAkD,SAAS,CAAC;EACtG,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAW,EAAE,CAAC;;EAE1D;EACA,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAkE,IAAI,CAAC;;EAE/G;EACA,MAAM,CAACiD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAgB,IAAI,CAAC;EAC7E,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;;EAEpE;EACA,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAO/C;IACDqB,MAAM,EAAE,KAAK;IACboC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;EAEF1D,SAAS,CAAC,MAAM;IACd,IAAIoB,MAAM,EAAE;MACVuC,gBAAgB,CAAC,CAAC;MAClB,IAAInC,OAAO,EAAE;QACXoC,gBAAgB,CAAC,CAAC;MACpB;MACAC,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACzC,MAAM,EAAEE,OAAO,CAAC,CAAC;EAErB,MAAMqC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM2B,QAAQ,GAAG,MAAMlD,kBAAkB,CAACmD,eAAe,CAACzC,OAAO,CAAC;MAClES,eAAe,CAAC+B,QAAQ,CAACE,IAAI,CAAC;MAC9BrB,WAAW,CAACmB,QAAQ,CAACE,IAAI,CAACC,IAAI,CAAC;MAC/BpB,kBAAkB,CAACiB,QAAQ,CAACE,IAAI,CAACE,WAAW,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdrD,KAAK,CAACqD,KAAK,CAAC,8BAA8B,CAAC;IAC7C,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMlD,kBAAkB,CAACwD,sBAAsB,CAAC9C,OAAO,CAAC;MACzEW,eAAe,CAAC6B,QAAQ,CAACE,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMN,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlD,kBAAkB,CAACiD,aAAa,CAACvC,OAAO,CAAC;MAChEyB,WAAW,CAACe,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC5B,QAAQ,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACpBzD,KAAK,CAACqD,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAMvD,kBAAkB,CAAC4D,WAAW,CAAClD,OAAO,EAAE;QAC5C2C,IAAI,EAAEvB,QAAQ;QACdwB,WAAW,EAAEtB;MACf,CAAC,CAAC;MAEFH,YAAY,CAAC,KAAK,CAAC;MACnB3B,KAAK,CAAC2D,OAAO,CAAC,4BAA4B,CAAC;MAC3Cd,gBAAgB,CAAC,CAAC;MAClBlC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdrD,KAAK,CAACqD,KAAK,CAAC,wBAAwB,CAAC;IACvC;EACF,CAAC;EAED,MAAMO,kBAAkB,GAAG,MAAAA,CAAOC,MAAc,EAAEC,SAAiB,KAAK;IACtE,IAAI,CAACC,OAAO,CAAC,mCAAmCD,SAAS,mBAAmB,CAAC,EAAE;MAC7E;IACF;IAEA,IAAI;MACFrC,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAEqC,MAAM,CAAC,CAAC;MACtC,MAAM/D,kBAAkB,CAACkE,YAAY,CAACxD,OAAO,EAAEqD,MAAM,CAAC;MACtD7D,KAAK,CAAC2D,OAAO,CAAC,GAAGG,SAAS,qBAAqB,CAAC;MAChDjB,gBAAgB,CAAC,CAAC;MAClBlC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAO0C,KAAU,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACnBlE,KAAK,CAACqD,KAAK,CAAC,EAAAY,eAAA,GAAAZ,KAAK,CAACL,QAAQ,cAAAiB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,yBAAyB,CAAC;IACxE,CAAC,SAAS;MACR1C,aAAa,CAACD,UAAU,CAAC4C,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKR,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAMS,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,EAACtC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEuC,SAAS,GAAE;MACxBvE,KAAK,CAACqD,KAAK,CAAC,CAAArB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEW,OAAO,KAAI,oBAAoB,CAAC;MACtD;IACF;IAEA,IAAI,CAACoB,OAAO,CAAC,0EAA0E,CAAC,EAAE;MACxF;IACF;IAEA,IAAI;MACF,MAAMjE,kBAAkB,CAAC0E,UAAU,CAAChE,OAAO,CAAC;MAC5CR,KAAK,CAAC2D,OAAO,CAAC,yBAAyB,CAAC;MACxCpD,OAAO,CAAC,CAAC;MACTI,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAO0C,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnB1E,KAAK,CAACqD,KAAK,CAAC,EAAAoB,gBAAA,GAAApB,KAAK,CAACL,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvB,IAAI,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAI,uBAAuB,CAAC;IACtE;EACF,CAAC;EAED,MAAMQ,wBAAwB,GAAG,MAAAA,CAAOC,SAAiB,EAAEC,QAAiB,EAAEf,SAAiB,KAAK;IAClG,IAAI;MACFrC,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAEoD,SAAS,CAAC,CAAC;MACzC,MAAM9E,kBAAkB,CAACgF,kBAAkB,CAACF,SAAS,EAAEC,QAAQ,CAAC;MAChE7E,KAAK,CAAC2D,OAAO,CAAC,gBAAgBkB,QAAQ,GAAG,UAAU,GAAG,UAAU,QAAQf,SAAS,EAAE,CAAC;MACpFhB,gBAAgB,CAAC,CAAC;MAClB,IAAI+B,QAAQ,EAAE;QACZhC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACtB;IACF,CAAC,CAAC,OAAOQ,KAAU,EAAE;MAAA,IAAA0B,gBAAA,EAAAC,qBAAA;MACnBhF,KAAK,CAACqD,KAAK,CAAC,EAAA0B,gBAAA,GAAA1B,KAAK,CAACL,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBb,MAAM,KAAI,gCAAgC,CAAC;IAC/E,CAAC,SAAS;MACR1C,aAAa,CAACD,UAAU,CAAC4C,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKO,SAAS,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMK,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAAC7C,gBAAgB,EAAE;MACrBpC,KAAK,CAACqD,KAAK,CAAC,2BAA2B,CAAC;MACxC;IACF;IAEA,IAAIf,oBAAoB,KAAK,UAAU,EAAE;MACvCtC,KAAK,CAACqD,KAAK,CAAC,iCAAiC,CAAC;MAC9C;IACF;IAEA,MAAM6B,QAAQ,GAAGlE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmE,OAAO,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKlD,gBAAgB,CAAC;IAChF,IAAI,CAAC8C,QAAQ,EAAE;MACblF,KAAK,CAACqD,KAAK,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEA,IAAI,CAACU,OAAO,CAAC,kDAAkDmB,QAAQ,CAACK,KAAK,wEAAwE,CAAC,EAAE;MACtJ;IACF;IAEA,IAAI;MACFhE,SAAS,CAAC,IAAI,CAAC;MACf,MAAMzB,kBAAkB,CAAC0F,iBAAiB,CAAChF,OAAO,EAAE4B,gBAAgB,CAAC;MACrEpC,KAAK,CAAC2D,OAAO,CAAC,4BAA4BuB,QAAQ,CAACK,KAAK,EAAE,CAAC;;MAE3D;MACApD,wBAAwB,CAAC,KAAK,CAAC;MAC/BE,mBAAmB,CAAC,IAAI,CAAC;MACzBE,uBAAuB,CAAC,EAAE,CAAC;;MAE3B;MACAM,gBAAgB,CAAC,CAAC;MAClBlC,cAAc,CAAC,CAAC;;MAEhB;MACA8E,UAAU,CAAC,MAAM;QACflF,OAAO,CAAC,CAAC;MACX,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAO8C,KAAU,EAAE;MAAA,IAAAqC,gBAAA,EAAAC,qBAAA;MACnB3F,KAAK,CAACqD,KAAK,CAAC,EAAAqC,gBAAA,GAAArC,KAAK,CAACL,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBxB,MAAM,KAAI,8BAA8B,CAAC;IAC7E,CAAC,SAAS;MACR5C,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,IAAI,CAACjB,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAK0F,SAAS,EAAC,gFAAgF;IAAAC,QAAA,eAC7F3F,OAAA;MAAK0F,SAAS,EAAC,mEAAmE;MAAAC,QAAA,gBAEhF3F,OAAA;QAAK0F,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7E3F,OAAA;UAAK0F,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C3F,OAAA;YAAK0F,SAAS,EAAC,+BAA+B;YAAAC,QAAA,eAC5C3F,OAAA,CAACL,KAAK;cAAC+F,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAI0F,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEpF;YAAS;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpE/F,OAAA;cAAG0F,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/F,OAAA;UACEgG,OAAO,EAAE3F,OAAQ;UACjBqF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7C3F,OAAA,CAACf,CAAC;YAACyG,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN/F,OAAA;QAAK0F,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACvC3F,OAAA;UAAK0F,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjC,CAAC,SAAS,EAAE,SAAS,EAAE,IAAInF,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAACyF,GAAG,CAAEC,GAAG,iBAC5ElG,OAAA;YAEEgG,OAAO,EAAEA,CAAA,KAAMnF,YAAY,CAACqF,GAAU,CAAE;YACxCR,SAAS,EAAE,uDACT9E,SAAS,KAAKsF,GAAG,GACb,qCAAqC,GACrC,sDAAsD,EACzD;YAAAP,QAAA,GAEFO,GAAG,KAAK,UAAU,IAAIlF,YAAY,CAACmF,MAAM,GAAG,CAAC,iBAC5CnG,OAAA;cAAM0F,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAC1E3E,YAAY,CAACmF;YAAM;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACP,EACAG,GAAG;UAAA,GAbCA,GAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcF,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/F,OAAA;QAAK0F,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAC9CzE,OAAO,gBACNlB,OAAA;UAAK0F,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD3F,OAAA;YAAK0F,SAAS,EAAC;UAAiE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,gBAEN/F,OAAA,CAAAE,SAAA;UAAAyF,QAAA,GAEG/E,SAAS,KAAK,SAAS,IAAIE,YAAY,iBACtCd,OAAA;YAAK0F,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB3F,OAAA;cAAK0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC3F,OAAA;gBAAK0F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD3F,OAAA;kBAAI0F,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC/DvF,OAAO,iBACNR,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,CAACD,SAAS,CAAE;kBACxCkE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAE5BnE,SAAS,gBAAGxB,OAAA,CAACf,CAAC;oBAACyG,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG/F,OAAA,CAACP,KAAK;oBAACiG,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACrEvE,SAAS,GAAG,QAAQ,GAAG,MAAM;gBAAA;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAELvE,SAAS,gBACRxB,OAAA;gBAAK0F,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3F,OAAA;kBAAA2F,QAAA,gBACE3F,OAAA;oBAAO0F,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR/F,OAAA;oBACEoG,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE3E,QAAS;oBAChB4E,QAAQ,EAAGC,CAAC,IAAK5E,WAAW,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC7CX,SAAS,EAAC,aAAa;oBACvBe,WAAW,EAAC;kBAAkB;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN/F,OAAA;kBAAA2F,QAAA,gBACE3F,OAAA;oBAAO0F,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR/F,OAAA;oBACEqG,KAAK,EAAEzE,eAAgB;oBACvB0E,QAAQ,EAAGC,CAAC,IAAK1E,kBAAkB,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACpDX,SAAS,EAAC,aAAa;oBACvBgB,IAAI,EAAE,CAAE;oBACRD,WAAW,EAAC;kBAAoC;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN/F,OAAA;kBACEgG,OAAO,EAAE1C,iBAAkB;kBAC3BoC,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAEvB3F,OAAA,CAACN,IAAI;oBAACgG,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,gBAEN/F,OAAA;gBAAK0F,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C3F,OAAA;kBAAA2F,QAAA,gBACE3F,OAAA;oBAAM0F,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5C/F,OAAA;oBAAM0F,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAE7E,YAAY,CAACmC;kBAAI;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACN/F,OAAA;kBAAA2F,QAAA,gBACE3F,OAAA;oBAAM0F,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C/F,OAAA;oBAAM0F,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAE7E,YAAY,CAAC6F;kBAAY;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACN/F,OAAA;kBAAA2F,QAAA,gBACE3F,OAAA;oBAAM0F,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7C/F,OAAA;oBAAM0F,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,gBAClD3F,OAAA,CAACd,KAAK;sBAACwG,SAAS,EAAC;oBAA8B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACjDjF,YAAY,CAAC8F,aAAa;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN/F,OAAA;kBAAA2F,QAAA,gBACE3F,OAAA;oBAAM0F,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C/F,OAAA;oBAAM0F,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAC/B,IAAIkB,IAAI,CAAC/F,YAAY,CAACgG,UAAU,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLjF,YAAY,CAACoC,WAAW,iBACvBlD,OAAA;kBAAK0F,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3F,OAAA;oBAAM0F,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnD/F,OAAA;oBAAG0F,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE7E,YAAY,CAACoC;kBAAW;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAnF,SAAS,KAAK,SAAS,IAAIE,YAAY,iBACtCd,OAAA;YAAK0F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3F,OAAA;cAAI0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5D/F,OAAA;cAAK0F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB7E,YAAY,CAACmE,OAAO,CAACgB,GAAG,CAAEe,MAAM,iBAC/BhH,OAAA;gBAA0B0F,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC/F3F,OAAA;kBAAK0F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3F,OAAA;oBAAK0F,SAAS,EAAC,wEAAwE;oBAAAC,QAAA,eACrF3F,OAAA;sBAAM0F,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EACnDqB,MAAM,CAAC3B,KAAK,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/F,OAAA;oBAAA2F,QAAA,gBACE3F,OAAA;sBAAG0F,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEqB,MAAM,CAAC3B;oBAAK;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D/F,OAAA;sBAAK0F,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GACzCqB,MAAM,CAAC5B,OAAO,KAAKtE,YAAY,CAACqG,UAAU,iBACzCnH,OAAA;wBAAM0F,SAAS,EAAC,mGAAmG;wBAAAC,QAAA,gBACjH3F,OAAA,CAACd,KAAK;0BAACwG,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,SAEpC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP,eACD/F,OAAA;wBAAM0F,SAAS,EAAE,WAAWsB,MAAM,CAACI,OAAO,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;wBAAAzB,QAAA,GAAC,YAC3E,EAAC0B,IAAI,CAACC,GAAG,CAACN,MAAM,CAACI,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACP,MAAM,CAACI,OAAO,IAAI,CAAC,GAAG,cAAc,GAAG,UAAU;sBAAA;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELvF,OAAO,IAAIwG,MAAM,CAAC5B,OAAO,KAAKtE,YAAY,CAACqG,UAAU,iBACpDnH,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACsD,MAAM,CAAC5B,OAAO,EAAE4B,MAAM,CAAC3B,KAAK,CAAE;kBAChEmC,QAAQ,EAAElG,UAAU,CAACmG,QAAQ,CAACT,MAAM,CAAC5B,OAAO,CAAE;kBAC9CM,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,eAElD3F,OAAA,CAACb,SAAS;oBAACuG,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CACT;cAAA,GA/BOiB,MAAM,CAAC5B,OAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgCnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAnF,SAAS,KAAK,UAAU,IAAIJ,OAAO,iBAClCR,OAAA;YAAK0F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3F,OAAA;cAAI0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACnE/E,YAAY,CAACmF,MAAM,KAAK,CAAC,gBACxBnG,OAAA;cAAK0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEN/F,OAAA;cAAK0F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB3E,YAAY,CAACiF,GAAG,CAAEyB,OAAO,iBACxB1H,OAAA;gBAA8B0F,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACjE3F,OAAA;kBAAK0F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD3F,OAAA;oBAAA2F,QAAA,gBACE3F,OAAA;sBAAG0F,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAE+B,OAAO,CAACC;oBAAU;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjE/F,OAAA;sBAAG0F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,YACzB,EAAC,IAAIkB,IAAI,CAACa,OAAO,CAACZ,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC,EACH2B,OAAO,CAACjF,OAAO,iBACdzC,OAAA;sBAAG0F,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GAAC,IAAC,EAAC+B,OAAO,CAACjF,OAAO,EAAC,IAAC;oBAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CACjE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN/F,OAAA;oBAAK0F,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B3F,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAMvB,wBAAwB,CAACiD,OAAO,CAACE,UAAU,EAAE,IAAI,EAAEF,OAAO,CAACC,UAAU,CAAE;sBACtFH,QAAQ,EAAElG,UAAU,CAACmG,QAAQ,CAACC,OAAO,CAACE,UAAU,CAAE;sBAClDlC,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,gBAE/B3F,OAAA,CAACZ,SAAS;wBAACsG,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAExC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT/F,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAMvB,wBAAwB,CAACiD,OAAO,CAACE,UAAU,EAAE,KAAK,EAAEF,OAAO,CAACC,UAAU,CAAE;sBACvFH,QAAQ,EAAElG,UAAU,CAACmG,QAAQ,CAACC,OAAO,CAACE,UAAU,CAAE;sBAClDlC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,gBAEjC3F,OAAA,CAACX,KAAK;wBAACqG,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA7BE2B,OAAO,CAACE,UAAU;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8BvB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGAnF,SAAS,KAAK,UAAU,iBACvBZ,OAAA;YAAK0F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3F,OAAA;cAAI0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAG5D,CAACvF,OAAO,iBACPR,OAAA;cAAK0F,SAAS,EAAC,gDAAgD;cAAAC,QAAA,eAC7D3F,OAAA;gBAAK0F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC3F,OAAA,CAACR,aAAa;kBAACkG,SAAS,EAAC;gBAA6B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzD/F,OAAA;kBAAK0F,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrB3F,OAAA;oBAAI0F,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzD/F,OAAA;oBAAG0F,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACrC,CAAA7D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEW,OAAO,KAAI;kBAAY;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,EACHjE,QAAQ,iBACP9B,OAAA;oBACEgG,OAAO,EAAE5B,gBAAiB;oBAC1BoD,QAAQ,EAAE,CAAC1F,QAAQ,CAACuC,SAAU;oBAC9BqB,SAAS,EAAE,QACT5D,QAAQ,CAACuC,SAAS,GACd,YAAY,GACZ,mEAAmE,EACtE;oBAAAsB,QAAA,gBAEH3F,OAAA,CAACT,MAAM;sBAACmG,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGAvF,OAAO,iBACNR,OAAA;cAAK0F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3F,OAAA;gBAAK0F,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnE3F,OAAA;kBAAK0F,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC3F,OAAA,CAACd,KAAK;oBAACwG,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpD/F,OAAA;oBAAA2F,QAAA,gBACE3F,OAAA;sBAAI0F,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/D/F,OAAA;sBAAG0F,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE5C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/F,OAAA;gBAAK0F,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC3F,OAAA;kBAAI0F,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtE/F,OAAA;kBAAG0F,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EAEH,CAAC/D,qBAAqB,gBACrBhC,OAAA;kBACE0F,SAAS,EAAC,eAAe;kBACzBM,OAAO,EAAEA,CAAA,KAAM/D,wBAAwB,CAAC,IAAI,CAAE;kBAAA0D,QAAA,gBAE9C3F,OAAA,CAACV,QAAQ;oBAACoG,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gBAET/F,OAAA;kBAAK0F,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB3F,OAAA;oBAAA2F,QAAA,gBACE3F,OAAA;sBAAO0F,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACR/F,OAAA;sBACEqG,KAAK,EAAEnE,gBAAgB,IAAI,EAAG;sBAC9BoE,QAAQ,EAAGC,CAAC,IAAKpE,mBAAmB,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,GAAGwB,QAAQ,CAACtB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,GAAG,IAAI,CAAE;sBACvFX,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAEvB3F,OAAA;wBAAQqG,KAAK,EAAC,EAAE;wBAAAV,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EAC3CjF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmE,OAAO,CACnBf,MAAM,CAAC8C,MAAM,IAAIA,MAAM,CAAC5B,OAAO,KAAKtE,YAAY,CAACqG,UAAU,CAAC,CAC5DlB,GAAG,CAACe,MAAM,iBACThH,OAAA;wBAA6BqG,KAAK,EAAEW,MAAM,CAAC5B,OAAQ;wBAAAO,QAAA,EAChDqB,MAAM,CAAC3B;sBAAK,GADF2B,MAAM,CAAC5B,OAAO;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEnB,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAEN/F,OAAA;oBAAA2F,QAAA,gBACE3F,OAAA;sBAAO0F,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACR/F,OAAA;sBACEoG,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAEjE,oBAAqB;sBAC5BkE,QAAQ,EAAGC,CAAC,IAAKlE,uBAAuB,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;sBACzDX,SAAS,EAAC,aAAa;sBACvBe,WAAW,EAAC;oBAAe;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAEN/F,OAAA;oBAAK0F,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,eAC7D3F,OAAA;sBAAK0F,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,gBACzC3F,OAAA,CAACR,aAAa;wBAACkG,SAAS,EAAC;sBAA6B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACzD/F,OAAA;wBAAK0F,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,gBACnC3F,OAAA;0BAAG0F,SAAS,EAAC,aAAa;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACvC/F,OAAA;0BAAA2F,QAAA,EAAG;wBAA+D;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN/F,OAAA;oBAAK0F,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B3F,OAAA;sBACEgG,OAAO,EAAEjB,uBAAwB;sBACjCyC,QAAQ,EAAEpG,MAAM,IAAI,CAACc,gBAAgB,IAAIE,oBAAoB,KAAK,UAAW;sBAC7EsD,SAAS,EAAE,GACTtE,MAAM,IAAI,CAACc,gBAAgB,IAAIE,oBAAoB,KAAK,UAAU,GAC9D,8CAA8C,GAC9C,wCAAwC,qDACQ;sBAAAuD,QAAA,gBAEtD3F,OAAA,CAACV,QAAQ;wBAACoG,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpC3E,MAAM,GAAG,iBAAiB,GAAG,oBAAoB;oBAAA;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACT/F,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAM;wBACb/D,wBAAwB,CAAC,KAAK,CAAC;wBAC/BE,mBAAmB,CAAC,IAAI,CAAC;wBACzBE,uBAAuB,CAAC,EAAE,CAAC;sBAC7B,CAAE;sBACFqD,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAC1B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA,eACD;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrF,EAAA,CA5kBIP,oBAAyD;EAAA,QAQ5CN,OAAO;AAAA;AAAAiI,EAAA,GARpB3H,oBAAyD;AA8kB/D,eAAeA,oBAAoB;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}