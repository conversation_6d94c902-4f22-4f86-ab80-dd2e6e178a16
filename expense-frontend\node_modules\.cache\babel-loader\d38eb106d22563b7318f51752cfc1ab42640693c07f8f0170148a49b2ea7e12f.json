{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\pages\\\\Groups.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { Plus, Users, UserPlus, Settings, Crown, Search, X } from 'lucide-react';\nimport { groupsAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport GroupManagementModal from '../components/GroupManagement/GroupManagementModal';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Groups = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [groups, setGroups] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showJoinModal, setShowJoinModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showManagementModal, setShowManagementModal] = useState(false);\n  const [selectedGroup, setSelectedGroup] = useState(null);\n  const [newGroupName, setNewGroupName] = useState('');\n  const [joinGroupId, setJoinGroupId] = useState('');\n  const [joinMessage, setJoinMessage] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  useEffect(() => {\n    loadGroups();\n  }, []);\n  const loadGroups = async () => {\n    try {\n      const response = await groupsAPI.getMyGroups();\n      setGroups(response.data);\n    } catch (error) {\n      toast.error('Failed to load groups');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateGroup = async e => {\n    e.preventDefault();\n    if (!newGroupName.trim()) return;\n    try {\n      const response = await groupsAPI.createGroup({\n        name: newGroupName\n      });\n      setGroups([...groups, response.data]);\n      setNewGroupName('');\n      setShowCreateModal(false);\n      toast.success('Group created successfully!');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Failed to create group');\n    }\n  };\n  const handleJoinGroup = async e => {\n    e.preventDefault();\n    if (!joinGroupId.trim()) return;\n    try {\n      const response = await groupsAPI.joinGroup({\n        group_id: parseInt(joinGroupId)\n      });\n      setGroups([...groups, response.data]);\n      setJoinGroupId('');\n      setShowJoinModal(false);\n      toast.success('Joined group successfully!');\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Failed to join group');\n    }\n  };\n  const handleViewDetails = groupId => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowDetailsModal(true);\n    }\n  };\n  const handleManageGroup = groupId => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowManagementModal(true);\n    }\n  };\n  const handleGroupUpdated = () => {\n    loadGroups(); // Refresh the groups list\n  };\n  const filteredGroups = groups.filter(group => group.name.toLowerCase().includes(searchTerm.toLowerCase()));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"Groups\",\n      subtitle: \"Manage your expense sharing groups\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"Groups\",\n    subtitle: \"Manage your expense sharing groups\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search groups...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowJoinModal(true),\n            className: \"btn-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), \"Join Group\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCreateModal(true),\n            className: \"btn-primary\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), \"Create Group\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), filteredGroups.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: searchTerm ? 'No groups found' : 'No groups yet'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-6\",\n          children: searchTerm ? 'Try adjusting your search terms' : 'Create your first group or join an existing one to start sharing expenses'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this), !searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateModal(true),\n          className: \"btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 17\n          }, this), \"Create Your First Group\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: filteredGroups.map(group => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card hover:shadow-lg transition-shadow\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-6 h-6 text-primary-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900\",\n                      children: group.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 27\n                    }, this), group.creator_id === (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                      children: [/*#__PURE__*/_jsxDEV(Crown, {\n                        className: \"w-3 h-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 31\n                      }, this), \"Owner\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"ID: \", group.id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleManageGroup(group.id),\n                className: \"btn-ghost p-2\",\n                title: \"Manage Group\",\n                children: /*#__PURE__*/_jsxDEV(Settings, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: group.members.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [group.members.slice(0, 3).map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-gray-600\",\n                      children: member.email.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm text-gray-700 truncate\",\n                    children: member.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 27\n                  }, this), member.id === group.creator_id && /*#__PURE__*/_jsxDEV(Crown, {\n                    className: \"w-3 h-3 text-yellow-500 ml-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 29\n                  }, this)]\n                }, member.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 25\n                }, this)), group.members.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"+\", group.members.length - 3, \" more members\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 pt-4 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleViewDetails(group.id),\n                className: \"w-full btn-ghost text-sm\",\n                children: \"View Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 17\n          }, this)\n        }, group.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), showCreateModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Create New Group\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreateGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"groupName\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Group Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"groupName\",\n                type: \"text\",\n                value: newGroupName,\n                onChange: e => setNewGroupName(e.target.value),\n                placeholder: \"Enter group name (e.g., Office Team, Roommates)\",\n                className: \"input-field\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowCreateModal(false),\n                className: \"flex-1 btn-secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"flex-1 btn-primary\",\n                children: \"Create Group\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), showJoinModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Join Existing Group\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleJoinGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"groupId\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Group ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"groupId\",\n                type: \"number\",\n                value: joinGroupId,\n                onChange: e => setJoinGroupId(e.target.value),\n                placeholder: \"Enter the group ID to join\",\n                className: \"input-field\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"Ask a group member for the group ID to join\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowJoinModal(false),\n                className: \"flex-1 btn-secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"flex-1 btn-primary\",\n                children: \"Join Group\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this), showDetailsModal && selectedGroup && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: selectedGroup.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDetailsModal(false),\n              className: \"text-gray-400 hover:text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-2\",\n                children: \"Group Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Group ID:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: selectedGroup.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Members:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: selectedGroup.members.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-3\",\n                children: \"Members\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: selectedGroup.members.map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-primary-600\",\n                        children: member.email.charAt(0).toUpperCase()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 360,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: member.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 29\n                      }, this), member.id === selectedGroup.creator_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-yellow-600 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Crown, {\n                          className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 368,\n                          columnNumber: 33\n                        }, this), \"Group Owner\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this)\n                }, member.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-3 pt-4 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowDetailsModal(false),\n                className: \"btn-secondary\",\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this), showManagementModal && selectedGroup && /*#__PURE__*/_jsxDEV(GroupManagementModal, {\n        isOpen: showManagementModal,\n        onClose: () => setShowManagementModal(false),\n        groupId: selectedGroup.id,\n        groupName: selectedGroup.name,\n        isOwner: selectedGroup.creator_id === (user === null || user === void 0 ? void 0 : user.id),\n        onGroupUpdated: handleGroupUpdated\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(Groups, \"Gtk0Ur4K0LXIHTE5dS0KEoe12gw=\", false, function () {\n  return [useAuth];\n});\n_c = Groups;\nexport default Groups;\nvar _c;\n$RefreshReg$(_c, \"Groups\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "Plus", "Users", "UserPlus", "Settings", "Crown", "Search", "X", "groupsAPI", "useAuth", "GroupManagementModal", "toast", "jsxDEV", "_jsxDEV", "Groups", "_s", "user", "groups", "setGroups", "loading", "setLoading", "showCreateModal", "setShowCreateModal", "showJoinModal", "setShowJoinModal", "showDetailsModal", "setShowDetailsModal", "showManagementModal", "setShowManagementModal", "selectedGroup", "setSelectedGroup", "newGroupName", "setNewGroupName", "joinGroupId", "setJoinGroupId", "joinMessage", "setJoinMessage", "searchTerm", "setSearchTerm", "loadGroups", "response", "getMyGroups", "data", "error", "handleCreateGroup", "e", "preventDefault", "trim", "createGroup", "name", "success", "_error$response", "_error$response$data", "detail", "handleJoinGroup", "joinGroup", "group_id", "parseInt", "_error$response2", "_error$response2$data", "handleViewDetails", "groupId", "group", "find", "g", "id", "handleManageGroup", "handleGroupUpdated", "filteredGroups", "filter", "toLowerCase", "includes", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onClick", "length", "map", "creator_id", "members", "slice", "member", "email", "char<PERSON>t", "toUpperCase", "onSubmit", "htmlFor", "required", "isOpen", "onClose", "groupName", "isOwner", "onGroupUpdated", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/pages/Groups.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport {\n  Plus,\n  Users,\n  UserPlus,\n  Settings,\n\n  Crown,\n  Search,\n  X\n} from 'lucide-react';\nimport { Group } from '../types';\nimport { groupsAPI, groupManagementAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport GroupManagementModal from '../components/GroupManagement/GroupManagementModal';\nimport toast from 'react-hot-toast';\n\nconst Groups: React.FC = () => {\n  const { user } = useAuth();\n  const [groups, setGroups] = useState<Group[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showJoinModal, setShowJoinModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showManagementModal, setShowManagementModal] = useState(false);\n  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);\n  const [newGroupName, setNewGroupName] = useState('');\n  const [joinGroupId, setJoinGroupId] = useState('');\n  const [joinMessage, setJoinMessage] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    loadGroups();\n  }, []);\n\n  const loadGroups = async () => {\n    try {\n      const response = await groupsAPI.getMyGroups();\n      setGroups(response.data);\n    } catch (error) {\n      toast.error('Failed to load groups');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateGroup = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newGroupName.trim()) return;\n\n    try {\n      const response = await groupsAPI.createGroup({ name: newGroupName });\n      setGroups([...groups, response.data]);\n      setNewGroupName('');\n      setShowCreateModal(false);\n      toast.success('Group created successfully!');\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to create group');\n    }\n  };\n\n  const handleJoinGroup = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!joinGroupId.trim()) return;\n\n    try {\n      const response = await groupsAPI.joinGroup({ group_id: parseInt(joinGroupId) });\n      setGroups([...groups, response.data]);\n      setJoinGroupId('');\n      setShowJoinModal(false);\n      toast.success('Joined group successfully!');\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to join group');\n    }\n  };\n\n  const handleViewDetails = (groupId: number) => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowDetailsModal(true);\n    }\n  };\n\n  const handleManageGroup = (groupId: number) => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowManagementModal(true);\n    }\n  };\n\n  const handleGroupUpdated = () => {\n    loadGroups(); // Refresh the groups list\n  };\n\n  const filteredGroups = groups.filter(group =>\n    group.name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) {\n    return (\n      <Layout title=\"Groups\" subtitle=\"Manage your expense sharing groups\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout title=\"Groups\" subtitle=\"Manage your expense sharing groups\">\n      <div className=\"space-y-6\">\n        {/* Header Actions */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search groups...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64\"\n            />\n          </div>\n          \n          <div className=\"flex gap-3\">\n            <button\n              onClick={() => setShowJoinModal(true)}\n              className=\"btn-secondary\"\n            >\n              <UserPlus className=\"w-4 h-4 mr-2\" />\n              Join Group\n            </button>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"btn-primary\"\n            >\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Create Group\n            </button>\n          </div>\n        </div>\n\n        {/* Groups Grid */}\n        {filteredGroups.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Users className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {searchTerm ? 'No groups found' : 'No groups yet'}\n            </h3>\n            <p className=\"text-gray-500 mb-6\">\n              {searchTerm \n                ? 'Try adjusting your search terms'\n                : 'Create your first group or join an existing one to start sharing expenses'\n              }\n            </p>\n            {!searchTerm && (\n              <button\n                onClick={() => setShowCreateModal(true)}\n                className=\"btn-primary\"\n              >\n                <Plus className=\"w-4 h-4 mr-2\" />\n                Create Your First Group\n              </button>\n            )}\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredGroups.map((group) => (\n              <div key={group.id} className=\"card hover:shadow-lg transition-shadow\">\n                <div className=\"card-content\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\">\n                        <Users className=\"w-6 h-6 text-primary-600\" />\n                      </div>\n                      <div className=\"ml-3\">\n                        <div className=\"flex items-center space-x-2\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">{group.name}</h3>\n                          {group.creator_id === user?.id && (\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                              <Crown className=\"w-3 h-3 mr-1\" />\n                              Owner\n                            </span>\n                          )}\n                        </div>\n                        <p className=\"text-sm text-gray-500\">ID: {group.id}</p>\n                      </div>\n                    </div>\n                    <button\n                      onClick={() => handleManageGroup(group.id)}\n                      className=\"btn-ghost p-2\"\n                      title=\"Manage Group\"\n                    >\n                      <Settings className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  \n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-gray-600\">Members</span>\n                      <span className=\"font-medium\">{group.members.length}</span>\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      {group.members.slice(0, 3).map((member) => (\n                        <div key={member.id} className=\"flex items-center\">\n                          <div className=\"w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-xs font-medium text-gray-600\">\n                              {member.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <span className=\"ml-2 text-sm text-gray-700 truncate\">\n                            {member.email}\n                          </span>\n                          {member.id === group.creator_id && (\n                            <Crown className=\"w-3 h-3 text-yellow-500 ml-1\" />\n                          )}\n                        </div>\n                      ))}\n                      {group.members.length > 3 && (\n                        <div className=\"text-xs text-gray-500\">\n                          +{group.members.length - 3} more members\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                    <button\n                      onClick={() => handleViewDetails(group.id)}\n                      className=\"w-full btn-ghost text-sm\"\n                    >\n                      View Details\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Create Group Modal */}\n        {showCreateModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Create New Group</h3>\n              <form onSubmit={handleCreateGroup}>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"groupName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Group Name\n                  </label>\n                  <input\n                    id=\"groupName\"\n                    type=\"text\"\n                    value={newGroupName}\n                    onChange={(e) => setNewGroupName(e.target.value)}\n                    placeholder=\"Enter group name (e.g., Office Team, Roommates)\"\n                    className=\"input-field\"\n                    required\n                  />\n                </div>\n                <div className=\"flex gap-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateModal(false)}\n                    className=\"flex-1 btn-secondary\"\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"flex-1 btn-primary\">\n                    Create Group\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Join Group Modal */}\n        {showJoinModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Join Existing Group</h3>\n              <form onSubmit={handleJoinGroup}>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"groupId\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Group ID\n                  </label>\n                  <input\n                    id=\"groupId\"\n                    type=\"number\"\n                    value={joinGroupId}\n                    onChange={(e) => setJoinGroupId(e.target.value)}\n                    placeholder=\"Enter the group ID to join\"\n                    className=\"input-field\"\n                    required\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    Ask a group member for the group ID to join\n                  </p>\n                </div>\n                <div className=\"flex gap-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowJoinModal(false)}\n                    className=\"flex-1 btn-secondary\"\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"flex-1 btn-primary\">\n                    Join Group\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Group Details Modal */}\n        {showDetailsModal && selectedGroup && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold text-gray-900\">{selectedGroup.name}</h3>\n                <button\n                  onClick={() => setShowDetailsModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n              </div>\n\n              <div className=\"space-y-6\">\n                {/* Group Info */}\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Group Information</h4>\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600\">Group ID:</span>\n                      <span className=\"ml-2 font-medium\">{selectedGroup.id}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">Members:</span>\n                      <span className=\"ml-2 font-medium\">{selectedGroup.members.length}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Members List */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Members</h4>\n                  <div className=\"space-y-2\">\n                    {selectedGroup.members.map((member) => (\n                      <div key={member.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-medium text-primary-600\">\n                              {member.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className=\"text-sm font-medium text-gray-900\">{member.email}</p>\n                            {member.id === selectedGroup.creator_id && (\n                              <p className=\"text-xs text-yellow-600 flex items-center\">\n                                <Crown className=\"w-3 h-3 mr-1\" />\n                                Group Owner\n                              </p>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Actions */}\n                <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n                  <button\n                    onClick={() => setShowDetailsModal(false)}\n                    className=\"btn-secondary\"\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Group Management Modal */}\n        {showManagementModal && selectedGroup && (\n          <GroupManagementModal\n            isOpen={showManagementModal}\n            onClose={() => setShowManagementModal(false)}\n            groupId={selectedGroup.id}\n            groupName={selectedGroup.name}\n            isOwner={selectedGroup.creator_id === user?.id}\n            onGroupUpdated={handleGroupUpdated}\n          />\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Groups;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SACEC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EAERC,KAAK,EACLC,MAAM,EACNC,CAAC,QACI,cAAc;AAErB,SAASC,SAAS,QAA4B,iBAAiB;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdwC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhC,SAAS,CAACiC,WAAW,CAAC,CAAC;MAC9CvB,SAAS,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhC,KAAK,CAACgC,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,iBAAiB,GAAG,MAAOC,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACf,YAAY,CAACgB,IAAI,CAAC,CAAC,EAAE;IAE1B,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMhC,SAAS,CAACwC,WAAW,CAAC;QAAEC,IAAI,EAAElB;MAAa,CAAC,CAAC;MACpEb,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEuB,QAAQ,CAACE,IAAI,CAAC,CAAC;MACrCV,eAAe,CAAC,EAAE,CAAC;MACnBV,kBAAkB,CAAC,KAAK,CAAC;MACzBX,KAAK,CAACuC,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAAQ,eAAA,EAAAC,oBAAA;MACnBzC,KAAK,CAACgC,KAAK,CAAC,EAAAQ,eAAA,GAAAR,KAAK,CAACH,QAAQ,cAAAW,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBT,IAAI,cAAAU,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,wBAAwB,CAAC;IACvE;EACF,CAAC;EAED,MAAMC,eAAe,GAAG,MAAOT,CAAkB,IAAK;IACpDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACb,WAAW,CAACc,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMhC,SAAS,CAAC+C,SAAS,CAAC;QAAEC,QAAQ,EAAEC,QAAQ,CAACxB,WAAW;MAAE,CAAC,CAAC;MAC/Ef,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEuB,QAAQ,CAACE,IAAI,CAAC,CAAC;MACrCR,cAAc,CAAC,EAAE,CAAC;MAClBV,gBAAgB,CAAC,KAAK,CAAC;MACvBb,KAAK,CAACuC,OAAO,CAAC,4BAA4B,CAAC;IAC7C,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACnBhD,KAAK,CAACgC,KAAK,CAAC,EAAAe,gBAAA,GAAAf,KAAK,CAACH,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBN,MAAM,KAAI,sBAAsB,CAAC;IACrE;EACF,CAAC;EAED,MAAMO,iBAAiB,GAAIC,OAAe,IAAK;IAC7C,MAAMC,KAAK,GAAG7C,MAAM,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;IAChD,IAAIC,KAAK,EAAE;MACThC,gBAAgB,CAACgC,KAAK,CAAC;MACvBpC,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMwC,iBAAiB,GAAIL,OAAe,IAAK;IAC7C,MAAMC,KAAK,GAAG7C,MAAM,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;IAChD,IAAIC,KAAK,EAAE;MACThC,gBAAgB,CAACgC,KAAK,CAAC;MACvBlC,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAMuC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5B,UAAU,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAM6B,cAAc,GAAGnD,MAAM,CAACoD,MAAM,CAACP,KAAK,IACxCA,KAAK,CAACb,IAAI,CAACqB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAC5D,CAAC;EAED,IAAInD,OAAO,EAAE;IACX,oBACEN,OAAA,CAACb,MAAM;MAACwE,KAAK,EAAC,QAAQ;MAACC,QAAQ,EAAC,oCAAoC;MAAAC,QAAA,eAClE7D,OAAA;QAAK8D,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpD7D,OAAA;UAAK8D,SAAS,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACElE,OAAA,CAACb,MAAM;IAACwE,KAAK,EAAC,QAAQ;IAACC,QAAQ,EAAC,oCAAoC;IAAAC,QAAA,eAClE7D,OAAA;MAAK8D,SAAS,EAAC,WAAW;MAAAD,QAAA,gBAExB7D,OAAA;QAAK8D,SAAS,EAAC,oEAAoE;QAAAD,QAAA,gBACjF7D,OAAA;UAAK8D,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB7D,OAAA,CAACP,MAAM;YAACqE,SAAS,EAAC;UAA0E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FlE,OAAA;YACEmE,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kBAAkB;YAC9BC,KAAK,EAAE7C,UAAW;YAClB8C,QAAQ,EAAGtC,CAAC,IAAKP,aAAa,CAACO,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;YAC/CP,SAAS,EAAC;UAAwI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlE,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB7D,OAAA;YACEwE,OAAO,EAAEA,CAAA,KAAM7D,gBAAgB,CAAC,IAAI,CAAE;YACtCmD,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAEzB7D,OAAA,CAACV,QAAQ;cAACwE,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlE,OAAA;YACEwE,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,IAAI,CAAE;YACxCqD,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAEvB7D,OAAA,CAACZ,IAAI;cAAC0E,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLX,cAAc,CAACkB,MAAM,KAAK,CAAC,gBAC1BzE,OAAA;QAAK8D,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChC7D,OAAA,CAACX,KAAK;UAACyE,SAAS,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DlE,OAAA;UAAI8D,SAAS,EAAC,wCAAwC;UAAAD,QAAA,EACnDrC,UAAU,GAAG,iBAAiB,GAAG;QAAe;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACLlE,OAAA;UAAG8D,SAAS,EAAC,oBAAoB;UAAAD,QAAA,EAC9BrC,UAAU,GACP,iCAAiC,GACjC;QAA2E;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE9E,CAAC,EACH,CAAC1C,UAAU,iBACVxB,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,IAAI,CAAE;UACxCqD,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAEvB7D,OAAA,CAACZ,IAAI;YAAC0E,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENlE,OAAA;QAAK8D,SAAS,EAAC,sDAAsD;QAAAD,QAAA,EAClEN,cAAc,CAACmB,GAAG,CAAEzB,KAAK,iBACxBjD,OAAA;UAAoB8D,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eACpE7D,OAAA;YAAK8D,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3B7D,OAAA;cAAK8D,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD7D,OAAA;gBAAK8D,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC7D,OAAA;kBAAK8D,SAAS,EAAC,sEAAsE;kBAAAD,QAAA,eACnF7D,OAAA,CAACX,KAAK;oBAACyE,SAAS,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNlE,OAAA;kBAAK8D,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBACnB7D,OAAA;oBAAK8D,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1C7D,OAAA;sBAAI8D,SAAS,EAAC,qCAAqC;sBAAAD,QAAA,EAAEZ,KAAK,CAACb;oBAAI;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACpEjB,KAAK,CAAC0B,UAAU,MAAKxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,EAAE,kBAC5BpD,OAAA;sBAAM8D,SAAS,EAAC,mGAAmG;sBAAAD,QAAA,gBACjH7D,OAAA,CAACR,KAAK;wBAACsE,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,SAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlE,OAAA;oBAAG8D,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,GAAC,MAAI,EAACZ,KAAK,CAACG,EAAE;kBAAA;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlE,OAAA;gBACEwE,OAAO,EAAEA,CAAA,KAAMnB,iBAAiB,CAACJ,KAAK,CAACG,EAAE,CAAE;gBAC3CU,SAAS,EAAC,eAAe;gBACzBH,KAAK,EAAC,cAAc;gBAAAE,QAAA,eAEpB7D,OAAA,CAACT,QAAQ;kBAACuE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENlE,OAAA;cAAK8D,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxB7D,OAAA;gBAAK8D,SAAS,EAAC,2CAA2C;gBAAAD,QAAA,gBACxD7D,OAAA;kBAAM8D,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9ClE,OAAA;kBAAM8D,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAEZ,KAAK,CAAC2B,OAAO,CAACH;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eAENlE,OAAA;gBAAK8D,SAAS,EAAC,WAAW;gBAAAD,QAAA,GACvBZ,KAAK,CAAC2B,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAEI,MAAM,iBACpC9E,OAAA;kBAAqB8D,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChD7D,OAAA;oBAAK8D,SAAS,EAAC,mEAAmE;oBAAAD,QAAA,eAChF7D,OAAA;sBAAM8D,SAAS,EAAC,mCAAmC;sBAAAD,QAAA,EAChDiB,MAAM,CAACC,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNlE,OAAA;oBAAM8D,SAAS,EAAC,qCAAqC;oBAAAD,QAAA,EAClDiB,MAAM,CAACC;kBAAK;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,EACNY,MAAM,CAAC1B,EAAE,KAAKH,KAAK,CAAC0B,UAAU,iBAC7B3E,OAAA,CAACR,KAAK;oBAACsE,SAAS,EAAC;kBAA8B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAClD;gBAAA,GAXOY,MAAM,CAAC1B,EAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYd,CACN,CAAC,EACDjB,KAAK,CAAC2B,OAAO,CAACH,MAAM,GAAG,CAAC,iBACvBzE,OAAA;kBAAK8D,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,GAAC,GACpC,EAACZ,KAAK,CAAC2B,OAAO,CAACH,MAAM,GAAG,CAAC,EAAC,eAC7B;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlE,OAAA;cAAK8D,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjD7D,OAAA;gBACEwE,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAACE,KAAK,CAACG,EAAE,CAAE;gBAC3CU,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,EACrC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAnEEjB,KAAK,CAACG,EAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoEb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGA1D,eAAe,iBACdR,OAAA;QAAK8D,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F7D,OAAA;UAAK8D,SAAS,EAAC,yCAAyC;UAAAD,QAAA,gBACtD7D,OAAA;YAAI8D,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ElE,OAAA;YAAMkF,QAAQ,EAAEnD,iBAAkB;YAAA8B,QAAA,gBAChC7D,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB7D,OAAA;gBAAOmF,OAAO,EAAC,WAAW;gBAACrB,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEpF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEoD,EAAE,EAAC,WAAW;gBACde,IAAI,EAAC,MAAM;gBACXE,KAAK,EAAEnD,YAAa;gBACpBoD,QAAQ,EAAGtC,CAAC,IAAKb,eAAe,CAACa,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;gBACjDD,WAAW,EAAC,iDAAiD;gBAC7DN,SAAS,EAAC,aAAa;gBACvBsB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlE,OAAA;cAAK8D,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB7D,OAAA;gBACEmE,IAAI,EAAC,QAAQ;gBACbK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,KAAK,CAAE;gBACzCqD,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACjC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlE,OAAA;gBAAQmE,IAAI,EAAC,QAAQ;gBAACL,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAxD,aAAa,iBACZV,OAAA;QAAK8D,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F7D,OAAA;UAAK8D,SAAS,EAAC,yCAAyC;UAAAD,QAAA,gBACtD7D,OAAA;YAAI8D,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFlE,OAAA;YAAMkF,QAAQ,EAAEzC,eAAgB;YAAAoB,QAAA,gBAC9B7D,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB7D,OAAA;gBAAOmF,OAAO,EAAC,SAAS;gBAACrB,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAElF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEoD,EAAE,EAAC,SAAS;gBACZe,IAAI,EAAC,QAAQ;gBACbE,KAAK,EAAEjD,WAAY;gBACnBkD,QAAQ,EAAGtC,CAAC,IAAKX,cAAc,CAACW,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;gBAChDD,WAAW,EAAC,4BAA4B;gBACxCN,SAAS,EAAC,aAAa;gBACvBsB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFlE,OAAA;gBAAG8D,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAE1C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNlE,OAAA;cAAK8D,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB7D,OAAA;gBACEmE,IAAI,EAAC,QAAQ;gBACbK,OAAO,EAAEA,CAAA,KAAM7D,gBAAgB,CAAC,KAAK,CAAE;gBACvCmD,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACjC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlE,OAAA;gBAAQmE,IAAI,EAAC,QAAQ;gBAACL,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAtD,gBAAgB,IAAII,aAAa,iBAChChB,OAAA;QAAK8D,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F7D,OAAA;UAAK8D,SAAS,EAAC,uEAAuE;UAAAD,QAAA,gBACpF7D,OAAA;YAAK8D,SAAS,EAAC,wCAAwC;YAAAD,QAAA,gBACrD7D,OAAA;cAAI8D,SAAS,EAAC,qCAAqC;cAAAD,QAAA,EAAE7C,aAAa,CAACoB;YAAI;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7ElE,OAAA;cACEwE,OAAO,EAAEA,CAAA,KAAM3D,mBAAmB,CAAC,KAAK,CAAE;cAC1CiD,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAE7C7D,OAAA,CAACN,CAAC;gBAACoE,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlE,OAAA;YAAK8D,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAExB7D,OAAA;cAAK8D,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC7D,OAAA;gBAAI8D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrElE,OAAA;gBAAK8D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,gBAC7C7D,OAAA;kBAAA6D,QAAA,gBACE7D,OAAA;oBAAM8D,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChDlE,OAAA;oBAAM8D,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,EAAE7C,aAAa,CAACoC;kBAAE;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNlE,OAAA;kBAAA6D,QAAA,gBACE7D,OAAA;oBAAM8D,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/ClE,OAAA;oBAAM8D,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,EAAE7C,aAAa,CAAC4D,OAAO,CAACH;kBAAM;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAI8D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DlE,OAAA;gBAAK8D,SAAS,EAAC,WAAW;gBAAAD,QAAA,EACvB7C,aAAa,CAAC4D,OAAO,CAACF,GAAG,CAAEI,MAAM,iBAChC9E,OAAA;kBAAqB8D,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,eAC1F7D,OAAA;oBAAK8D,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,gBAChC7D,OAAA;sBAAK8D,SAAS,EAAC,sEAAsE;sBAAAD,QAAA,eACnF7D,OAAA;wBAAM8D,SAAS,EAAC,sCAAsC;wBAAAD,QAAA,EACnDiB,MAAM,CAACC,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;sBAAC;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNlE,OAAA;sBAAK8D,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBACnB7D,OAAA;wBAAG8D,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,EAAEiB,MAAM,CAACC;sBAAK;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAClEY,MAAM,CAAC1B,EAAE,KAAKpC,aAAa,CAAC2D,UAAU,iBACrC3E,OAAA;wBAAG8D,SAAS,EAAC,2CAA2C;wBAAAD,QAAA,gBACtD7D,OAAA,CAACR,KAAK;0BAACsE,SAAS,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAEpC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAhBEY,MAAM,CAAC1B,EAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBd,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlE,OAAA;cAAK8D,SAAS,EAAC,0DAA0D;cAAAD,QAAA,eACvE7D,OAAA;gBACEwE,OAAO,EAAEA,CAAA,KAAM3D,mBAAmB,CAAC,KAAK,CAAE;gBAC1CiD,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAC1B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGApD,mBAAmB,IAAIE,aAAa,iBACnChB,OAAA,CAACH,oBAAoB;QACnBwF,MAAM,EAAEvE,mBAAoB;QAC5BwE,OAAO,EAAEA,CAAA,KAAMvE,sBAAsB,CAAC,KAAK,CAAE;QAC7CiC,OAAO,EAAEhC,aAAa,CAACoC,EAAG;QAC1BmC,SAAS,EAAEvE,aAAa,CAACoB,IAAK;QAC9BoD,OAAO,EAAExE,aAAa,CAAC2D,UAAU,MAAKxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,EAAE,CAAC;QAC/CqC,cAAc,EAAEnC;MAAmB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAChE,EAAA,CApYID,MAAgB;EAAA,QACHL,OAAO;AAAA;AAAA8F,EAAA,GADpBzF,MAAgB;AAsYtB,eAAeA,MAAM;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}