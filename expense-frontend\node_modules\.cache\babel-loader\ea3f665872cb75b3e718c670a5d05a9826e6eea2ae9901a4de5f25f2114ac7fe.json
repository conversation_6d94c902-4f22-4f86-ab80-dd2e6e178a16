{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Hook for automatic data refreshing when notifications indicate data changes\n */\n\nimport { useEffect, useCallback } from 'react';\nimport { notificationService } from '../services/notificationService';\nexport const useAutoRefresh = (refreshCallback, dependencies = []) => {\n  _s();\n  const memoizedRefresh = useCallback(refreshCallback, [refreshCallback, ...dependencies]);\n  useEffect(() => {\n    // Subscribe to refresh events from notification service\n    const unsubscribe = notificationService.subscribeToRefresh(() => {\n      // Add a small delay to ensure backend has processed the change\n      setTimeout(() => {\n        memoizedRefresh();\n      }, 500);\n    });\n    return unsubscribe;\n  }, [memoizedRefresh]);\n\n  // Also refresh on interval for real-time updates (every 30 seconds)\n  useEffect(() => {\n    const interval = setInterval(() => {\n      memoizedRefresh();\n    }, 30000); // 30 seconds\n\n    return () => clearInterval(interval);\n  }, [memoizedRefresh]);\n};\n_s(useAutoRefresh, \"459UZlJ8gZV3deOfpzSfdvoHMB8=\");\nexport default useAutoRefresh;", "map": {"version": 3, "names": ["useEffect", "useCallback", "notificationService", "useAutoRefresh", "refreshCallback", "dependencies", "_s", "memoizedRefresh", "unsubscribe", "subscribeToRefresh", "setTimeout", "interval", "setInterval", "clearInterval"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/hooks/useAutoRefresh.ts"], "sourcesContent": ["/**\n * Hook for automatic data refreshing when notifications indicate data changes\n */\n\nimport { useEffect, useCallback } from 'react';\nimport { notificationService } from '../services/notificationService';\n\nexport const useAutoRefresh = (refreshCallback: () => void, dependencies: any[] = []) => {\n  const memoizedRefresh = useCallback(refreshCallback, [refreshCallback, ...dependencies]);\n\n  useEffect(() => {\n    // Subscribe to refresh events from notification service\n    const unsubscribe = notificationService.subscribeToRefresh(() => {\n      // Add a small delay to ensure backend has processed the change\n      setTimeout(() => {\n        memoizedRefresh();\n      }, 500);\n    });\n\n    return unsubscribe;\n  }, [memoizedRefresh]);\n\n  // Also refresh on interval for real-time updates (every 30 seconds)\n  useEffect(() => {\n    const interval = setInterval(() => {\n      memoizedRefresh();\n    }, 30000); // 30 seconds\n\n    return () => clearInterval(interval);\n  }, [memoizedRefresh]);\n};\n\nexport default useAutoRefresh;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,mBAAmB,QAAQ,iCAAiC;AAErE,OAAO,MAAMC,cAAc,GAAGA,CAACC,eAA2B,EAAEC,YAAmB,GAAG,EAAE,KAAK;EAAAC,EAAA;EACvF,MAAMC,eAAe,GAAGN,WAAW,CAACG,eAAe,EAAE,CAACA,eAAe,EAAE,GAAGC,YAAY,CAAC,CAAC;EAExFL,SAAS,CAAC,MAAM;IACd;IACA,MAAMQ,WAAW,GAAGN,mBAAmB,CAACO,kBAAkB,CAAC,MAAM;MAC/D;MACAC,UAAU,CAAC,MAAM;QACfH,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAOC,WAAW;EACpB,CAAC,EAAE,CAACD,eAAe,CAAC,CAAC;;EAErB;EACAP,SAAS,CAAC,MAAM;IACd,MAAMW,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCL,eAAe,CAAC,CAAC;IACnB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMM,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACJ,eAAe,CAAC,CAAC;AACvB,CAAC;AAACD,EAAA,CAvBWH,cAAc;AAyB3B,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}