{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\pages\\\\Expenses.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { Plus, Receipt, DollarSign, Calendar, Users, Filter, Search } from 'lucide-react';\nimport { groupsAPI, expensesAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatCurrency, formatDate } from '../utils/formatters';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport { notificationService } from '../services/notificationService';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Expenses = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [groups, setGroups] = useState([]);\n  const [expenses, setExpenses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedGroup, setSelectedGroup] = useState('all');\n\n  // Form state\n  const [formData, setFormData] = useState({\n    group_id: '',\n    total: '',\n    description: ''\n  });\n  const loadData = async () => {\n    try {\n      const [groupsRes, expensesRes] = await Promise.all([groupsAPI.getMyGroups(), expensesAPI.getHistory()]);\n      setGroups(groupsRes.data);\n      setExpenses(expensesRes.data);\n    } catch (error) {\n      toast.error('Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadData, []);\n  const handleCreateExpense = async e => {\n    e.preventDefault();\n    if (!formData.group_id || !formData.total || !formData.description) {\n      toast.error('Please fill in all fields');\n      return;\n    }\n    try {\n      const expenseData = {\n        group_id: parseInt(formData.group_id),\n        total: parseFloat(formData.total),\n        description: formData.description\n      };\n      const response = await expensesAPI.createExpense(expenseData);\n\n      // Add the new expense to the list\n      setExpenses([response.data, ...expenses]);\n      setFormData({\n        group_id: '',\n        total: '',\n        description: ''\n      });\n      setShowCreateModal(false);\n\n      // Trigger notification for expense creation\n      notificationService.simulateApprovalRequired({\n        description: response.data.description,\n        amount: parseFloat(formData.total),\n        payer: (user === null || user === void 0 ? void 0 : user.email) || 'You'\n      });\n      toast.success('Expense created successfully! Group members will be notified for approval.');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Failed to create expense');\n    }\n  };\n  const filteredExpenses = expenses.filter(expense => {\n    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const selectedGroupData = groups.find(g => g.name === selectedGroup);\n    const matchesGroup = selectedGroup === 'all' || expense.group_id === (selectedGroupData === null || selectedGroupData === void 0 ? void 0 : selectedGroupData.id);\n    return matchesSearch && matchesGroup;\n  });\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"Expenses\",\n      subtitle: \"Track and manage your shared expenses\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"Expenses\",\n    subtitle: \"Track and manage your shared expenses\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search expenses...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedGroup,\n              onChange: e => setSelectedGroup(e.target.value),\n              className: \"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Groups\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), groups.map(group => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: group.name,\n                children: group.name\n              }, group.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateModal(true),\n          className: \"btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), \"Add Expense\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), filteredExpenses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Receipt, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: searchTerm || selectedGroup !== 'all' ? 'No expenses found' : 'No expenses yet'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-6\",\n          children: searchTerm || selectedGroup !== 'all' ? 'Try adjusting your search or filter' : 'Start by adding your first expense to track shared costs'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), !searchTerm && selectedGroup === 'all' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateModal(true),\n          className: \"btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 17\n          }, this), \"Add Your First Expense\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: filteredExpenses.map(expense => {\n          var _groups$find;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card hover:shadow-lg transition-shadow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(Receipt, {\n                      className: \"w-6 h-6 text-primary-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900\",\n                      children: expense.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 mt-1 text-sm text-gray-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Users, {\n                          className: \"w-4 h-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 191,\n                          columnNumber: 29\n                        }, this), ((_groups$find = groups.find(g => g.id === expense.group_id)) === null || _groups$find === void 0 ? void 0 : _groups$find.name) || 'Unknown Group']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          className: \"w-4 h-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 195,\n                          columnNumber: 29\n                        }, this), formatDate(expense.created_at)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 194,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Paid by \", expense.payer_id === (user === null || user === void 0 ? void 0 : user.id) ? 'You' : 'Someone else']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-gray-900\",\n                    children: formatCurrency(expense.total)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"Split \", expense.shares.length + 1, \" ways\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)\n          }, expense.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), showCreateModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Add New Expense\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreateExpense,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"group\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Group\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"group\",\n                  value: formData.group_id,\n                  onChange: e => setFormData({\n                    ...formData,\n                    group_id: e.target.value\n                  }),\n                  className: \"input-field\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select a group\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), groups.map(group => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: group.id,\n                    children: group.name\n                  }, group.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"description\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"description\",\n                  type: \"text\",\n                  value: formData.description,\n                  onChange: e => setFormData({\n                    ...formData,\n                    description: e.target.value\n                  }),\n                  placeholder: \"What was this expense for?\",\n                  className: \"input-field\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"amount\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Total Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"amount\",\n                    type: \"number\",\n                    step: \"0.01\",\n                    min: \"0\",\n                    value: formData.total,\n                    onChange: e => setFormData({\n                      ...formData,\n                      total: e.target.value\n                    }),\n                    placeholder: \"0.00\",\n                    className: \"input-field pl-10\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3 mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowCreateModal(false),\n                className: \"flex-1 btn-secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"flex-1 btn-primary\",\n                children: \"Add Expense\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(Expenses, \"uPlaUZ1s+fUZ2rJOUQGcwJDTwfE=\", false, function () {\n  return [useAuth, useAutoRefresh];\n});\n_c = Expenses;\nexport default Expenses;\nvar _c;\n$RefreshReg$(_c, \"Expenses\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "Plus", "Receipt", "DollarSign", "Calendar", "Users", "Filter", "Search", "groupsAPI", "expensesAPI", "useAuth", "formatCurrency", "formatDate", "useAutoRefresh", "notificationService", "toast", "jsxDEV", "_jsxDEV", "Expenses", "_s", "user", "groups", "setGroups", "expenses", "setExpenses", "loading", "setLoading", "showCreateModal", "setShowCreateModal", "searchTerm", "setSearchTerm", "selectedGroup", "setSelectedGroup", "formData", "setFormData", "group_id", "total", "description", "loadData", "groupsRes", "expensesRes", "Promise", "all", "getMyGroups", "getHistory", "data", "error", "handleCreateExpense", "e", "preventDefault", "expenseData", "parseInt", "parseFloat", "response", "createExpense", "simulateApprovalRequired", "amount", "payer", "email", "success", "_error$response", "_error$response$data", "detail", "filteredExpenses", "filter", "expense", "matchesSearch", "toLowerCase", "includes", "selectedGroupData", "find", "g", "name", "matchesGroup", "id", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "map", "group", "onClick", "length", "_groups$find", "created_at", "payer_id", "shares", "onSubmit", "htmlFor", "required", "step", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/pages/Expenses.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { \n  Plus, \n  Receipt, \n  DollarSign,\n  Calendar,\n  Users,\n  Filter,\n  Search\n} from 'lucide-react';\nimport { Group, CreateExpenseRequest, Expense } from '../types';\nimport { groupsAPI, expensesAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatCurrency, formatDate } from '../utils/formatters';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport { notificationService } from '../services/notificationService';\nimport toast from 'react-hot-toast';\n\nconst Expenses: React.FC = () => {\n  const { user } = useAuth();\n  const [groups, setGroups] = useState<Group[]>([]);\n  const [expenses, setExpenses] = useState<Expense[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedGroup, setSelectedGroup] = useState<string>('all');\n  \n  // Form state\n  const [formData, setFormData] = useState({\n    group_id: '',\n    total: '',\n    description: ''\n  });\n\n  const loadData = async () => {\n    try {\n      const [groupsRes, expensesRes] = await Promise.all([\n        groupsAPI.getMyGroups(),\n        expensesAPI.getHistory()\n      ]);\n\n      setGroups(groupsRes.data);\n      setExpenses(expensesRes.data);\n\n    } catch (error) {\n      toast.error('Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadData, []);\n\n  const handleCreateExpense = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.group_id || !formData.total || !formData.description) {\n      toast.error('Please fill in all fields');\n      return;\n    }\n\n    try {\n      const expenseData: CreateExpenseRequest = {\n        group_id: parseInt(formData.group_id),\n        total: parseFloat(formData.total),\n        description: formData.description\n      };\n\n      const response = await expensesAPI.createExpense(expenseData);\n\n      // Add the new expense to the list\n      setExpenses([response.data, ...expenses]);\n      setFormData({ group_id: '', total: '', description: '' });\n      setShowCreateModal(false);\n\n      // Trigger notification for expense creation\n      notificationService.simulateApprovalRequired({\n        description: response.data.description,\n        amount: parseFloat(formData.total),\n        payer: user?.email || 'You'\n      });\n\n      toast.success('Expense created successfully! Group members will be notified for approval.');\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to create expense');\n    }\n  };\n\n  const filteredExpenses = expenses.filter(expense => {\n    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const selectedGroupData = groups.find(g => g.name === selectedGroup);\n    const matchesGroup = selectedGroup === 'all' || expense.group_id === selectedGroupData?.id;\n    return matchesSearch && matchesGroup;\n  });\n\n  if (loading) {\n    return (\n      <Layout title=\"Expenses\" subtitle=\"Track and manage your shared expenses\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout title=\"Expenses\" subtitle=\"Track and manage your shared expenses\">\n      <div className=\"space-y-6\">\n        {/* Header Actions */}\n        <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search expenses...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64\"\n              />\n            </div>\n            \n            <div className=\"relative\">\n              <Filter className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n              <select\n                value={selectedGroup}\n                onChange={(e) => setSelectedGroup(e.target.value)}\n                className=\"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white\"\n              >\n                <option value=\"all\">All Groups</option>\n                {groups.map(group => (\n                  <option key={group.id} value={group.name}>{group.name}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n          \n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"btn-primary\"\n          >\n            <Plus className=\"w-4 h-4 mr-2\" />\n            Add Expense\n          </button>\n        </div>\n\n        {/* Expenses List */}\n        {filteredExpenses.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Receipt className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {searchTerm || selectedGroup !== 'all' ? 'No expenses found' : 'No expenses yet'}\n            </h3>\n            <p className=\"text-gray-500 mb-6\">\n              {searchTerm || selectedGroup !== 'all'\n                ? 'Try adjusting your search or filter'\n                : 'Start by adding your first expense to track shared costs'\n              }\n            </p>\n            {!searchTerm && selectedGroup === 'all' && (\n              <button\n                onClick={() => setShowCreateModal(true)}\n                className=\"btn-primary\"\n              >\n                <Plus className=\"w-4 h-4 mr-2\" />\n                Add Your First Expense\n              </button>\n            )}\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {filteredExpenses.map((expense) => (\n              <div key={expense.id} className=\"card hover:shadow-lg transition-shadow\">\n                <div className=\"card-content\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\">\n                        <Receipt className=\"w-6 h-6 text-primary-600\" />\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <h3 className=\"text-lg font-semibold text-gray-900\">{expense.description}</h3>\n                        <div className=\"flex items-center space-x-4 mt-1 text-sm text-gray-500\">\n                          <div className=\"flex items-center\">\n                            <Users className=\"w-4 h-4 mr-1\" />\n                            {groups.find(g => g.id === expense.group_id)?.name || 'Unknown Group'}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"w-4 h-4 mr-1\" />\n                            {formatDate(expense.created_at)}\n                          </div>\n                          <span>Paid by {expense.payer_id === user?.id ? 'You' : 'Someone else'}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        {formatCurrency(expense.total)}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        Split {expense.shares.length + 1} ways\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Create Expense Modal */}\n        {showCreateModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Add New Expense</h3>\n              <form onSubmit={handleCreateExpense}>\n                <div className=\"space-y-4\">\n                  <div>\n                    <label htmlFor=\"group\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Group\n                    </label>\n                    <select\n                      id=\"group\"\n                      value={formData.group_id}\n                      onChange={(e) => setFormData({...formData, group_id: e.target.value})}\n                      className=\"input-field\"\n                      required\n                    >\n                      <option value=\"\">Select a group</option>\n                      {groups.map(group => (\n                        <option key={group.id} value={group.id}>{group.name}</option>\n                      ))}\n                    </select>\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Description\n                    </label>\n                    <input\n                      id=\"description\"\n                      type=\"text\"\n                      value={formData.description}\n                      onChange={(e) => setFormData({...formData, description: e.target.value})}\n                      placeholder=\"What was this expense for?\"\n                      className=\"input-field\"\n                      required\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Total Amount\n                    </label>\n                    <div className=\"relative\">\n                      <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n                      <input\n                        id=\"amount\"\n                        type=\"number\"\n                        step=\"0.01\"\n                        min=\"0\"\n                        value={formData.total}\n                        onChange={(e) => setFormData({...formData, total: e.target.value})}\n                        placeholder=\"0.00\"\n                        className=\"input-field pl-10\"\n                        required\n                      />\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex gap-3 mt-6\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateModal(false)}\n                    className=\"flex-1 btn-secondary\"\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"flex-1 btn-primary\">\n                    Add Expense\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Expenses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SACEC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,MAAM,QACD,cAAc;AAErB,SAASC,SAAS,EAAEC,WAAW,QAAQ,iBAAiB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,cAAc,EAAEC,UAAU,QAAQ,qBAAqB;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAS,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC;IACvCqC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAM,CAACC,SAAS,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjDlC,SAAS,CAACmC,WAAW,CAAC,CAAC,EACvBlC,WAAW,CAACmC,UAAU,CAAC,CAAC,CACzB,CAAC;MAEFtB,SAAS,CAACiB,SAAS,CAACM,IAAI,CAAC;MACzBrB,WAAW,CAACgB,WAAW,CAACK,IAAI,CAAC;IAE/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd/B,KAAK,CAAC+B,KAAK,CAAC,qBAAqB,CAAC;IACpC,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED3B,SAAS,CAAC,MAAM;IACduC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzB,cAAc,CAACyB,QAAQ,EAAE,EAAE,CAAC;EAE5B,MAAMS,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAChB,QAAQ,CAACE,QAAQ,IAAI,CAACF,QAAQ,CAACG,KAAK,IAAI,CAACH,QAAQ,CAACI,WAAW,EAAE;MAClEtB,KAAK,CAAC+B,KAAK,CAAC,2BAA2B,CAAC;MACxC;IACF;IAEA,IAAI;MACF,MAAMI,WAAiC,GAAG;QACxCf,QAAQ,EAAEgB,QAAQ,CAAClB,QAAQ,CAACE,QAAQ,CAAC;QACrCC,KAAK,EAAEgB,UAAU,CAACnB,QAAQ,CAACG,KAAK,CAAC;QACjCC,WAAW,EAAEJ,QAAQ,CAACI;MACxB,CAAC;MAED,MAAMgB,QAAQ,GAAG,MAAM5C,WAAW,CAAC6C,aAAa,CAACJ,WAAW,CAAC;;MAE7D;MACA1B,WAAW,CAAC,CAAC6B,QAAQ,CAACR,IAAI,EAAE,GAAGtB,QAAQ,CAAC,CAAC;MACzCW,WAAW,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,WAAW,EAAE;MAAG,CAAC,CAAC;MACzDT,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACAd,mBAAmB,CAACyC,wBAAwB,CAAC;QAC3ClB,WAAW,EAAEgB,QAAQ,CAACR,IAAI,CAACR,WAAW;QACtCmB,MAAM,EAAEJ,UAAU,CAACnB,QAAQ,CAACG,KAAK,CAAC;QAClCqB,KAAK,EAAE,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,KAAK,KAAI;MACxB,CAAC,CAAC;MAEF3C,KAAK,CAAC4C,OAAO,CAAC,4EAA4E,CAAC;IAC7F,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACnB9C,KAAK,CAAC+B,KAAK,CAAC,EAAAc,eAAA,GAAAd,KAAK,CAACO,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,0BAA0B,CAAC;IACzE;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGxC,QAAQ,CAACyC,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAGD,OAAO,CAAC5B,WAAW,CAAC8B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC;IAC1F,MAAME,iBAAiB,GAAGhD,MAAM,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKzC,aAAa,CAAC;IACpE,MAAM0C,YAAY,GAAG1C,aAAa,KAAK,KAAK,IAAIkC,OAAO,CAAC9B,QAAQ,MAAKkC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEK,EAAE;IAC1F,OAAOR,aAAa,IAAIO,YAAY;EACtC,CAAC,CAAC;EAEF,IAAIhD,OAAO,EAAE;IACX,oBACER,OAAA,CAACjB,MAAM;MAAC2E,KAAK,EAAC,UAAU;MAACC,QAAQ,EAAC,uCAAuC;MAAAC,QAAA,eACvE5D,OAAA;QAAK6D,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpD5D,OAAA;UAAK6D,SAAS,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACEjE,OAAA,CAACjB,MAAM;IAAC2E,KAAK,EAAC,UAAU;IAACC,QAAQ,EAAC,uCAAuC;IAAAC,QAAA,eACvE5D,OAAA;MAAK6D,SAAS,EAAC,WAAW;MAAAD,QAAA,gBAExB5D,OAAA;QAAK6D,SAAS,EAAC,oEAAoE;QAAAD,QAAA,gBACjF5D,OAAA;UAAK6D,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9C5D,OAAA;YAAK6D,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACvB5D,OAAA,CAACV,MAAM;cAACuE,SAAS,EAAC;YAA0E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/FjE,OAAA;cACEkE,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,oBAAoB;cAChCC,KAAK,EAAExD,UAAW;cAClByD,QAAQ,EAAGtC,CAAC,IAAKlB,aAAa,CAACkB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;cAC/CP,SAAS,EAAC;YAAwI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjE,OAAA;YAAK6D,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACvB5D,OAAA,CAACX,MAAM;cAACwE,SAAS,EAAC;YAA0E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/FjE,OAAA;cACEoE,KAAK,EAAEtD,aAAc;cACrBuD,QAAQ,EAAGtC,CAAC,IAAKhB,gBAAgB,CAACgB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;cAClDP,SAAS,EAAC,4JAA4J;cAAAD,QAAA,gBAEtK5D,OAAA;gBAAQoE,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACtC7D,MAAM,CAACmE,GAAG,CAACC,KAAK,iBACfxE,OAAA;gBAAuBoE,KAAK,EAAEI,KAAK,CAACjB,IAAK;gBAAAK,QAAA,EAAEY,KAAK,CAACjB;cAAI,GAAxCiB,KAAK,CAACf,EAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAyC,CAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjE,OAAA;UACEyE,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,IAAI,CAAE;UACxCkD,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAEvB5D,OAAA,CAAChB,IAAI;YAAC6E,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLnB,gBAAgB,CAAC4B,MAAM,KAAK,CAAC,gBAC5B1E,OAAA;QAAK6D,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChC5D,OAAA,CAACf,OAAO;UAAC4E,SAAS,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DjE,OAAA;UAAI6D,SAAS,EAAC,wCAAwC;UAAAD,QAAA,EACnDhD,UAAU,IAAIE,aAAa,KAAK,KAAK,GAAG,mBAAmB,GAAG;QAAiB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACLjE,OAAA;UAAG6D,SAAS,EAAC,oBAAoB;UAAAD,QAAA,EAC9BhD,UAAU,IAAIE,aAAa,KAAK,KAAK,GAClC,qCAAqC,GACrC;QAA0D;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE7D,CAAC,EACH,CAACrD,UAAU,IAAIE,aAAa,KAAK,KAAK,iBACrCd,OAAA;UACEyE,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,IAAI,CAAE;UACxCkD,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAEvB5D,OAAA,CAAChB,IAAI;YAAC6E,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENjE,OAAA;QAAK6D,SAAS,EAAC,WAAW;QAAAD,QAAA,EACvBd,gBAAgB,CAACyB,GAAG,CAAEvB,OAAO;UAAA,IAAA2B,YAAA;UAAA,oBAC5B3E,OAAA;YAAsB6D,SAAS,EAAC,wCAAwC;YAAAD,QAAA,eACtE5D,OAAA;cAAK6D,SAAS,EAAC,cAAc;cAAAD,QAAA,eAC3B5D,OAAA;gBAAK6D,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,gBAChD5D,OAAA;kBAAK6D,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C5D,OAAA;oBAAK6D,SAAS,EAAC,sEAAsE;oBAAAD,QAAA,eACnF5D,OAAA,CAACf,OAAO;sBAAC4E,SAAS,EAAC;oBAA0B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eAENjE,OAAA;oBAAK6D,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrB5D,OAAA;sBAAI6D,SAAS,EAAC,qCAAqC;sBAAAD,QAAA,EAAEZ,OAAO,CAAC5B;oBAAW;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9EjE,OAAA;sBAAK6D,SAAS,EAAC,wDAAwD;sBAAAD,QAAA,gBACrE5D,OAAA;wBAAK6D,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChC5D,OAAA,CAACZ,KAAK;0BAACyE,SAAS,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACjC,EAAAU,YAAA,GAAAvE,MAAM,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,EAAE,KAAKT,OAAO,CAAC9B,QAAQ,CAAC,cAAAyD,YAAA,uBAA3CA,YAAA,CAA6CpB,IAAI,KAAI,eAAe;sBAAA;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE,CAAC,eACNjE,OAAA;wBAAK6D,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,gBAChC5D,OAAA,CAACb,QAAQ;0BAAC0E,SAAS,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACpCtE,UAAU,CAACqD,OAAO,CAAC4B,UAAU,CAAC;sBAAA;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACNjE,OAAA;wBAAA4D,QAAA,GAAM,UAAQ,EAACZ,OAAO,CAAC6B,QAAQ,MAAK1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,EAAE,IAAG,KAAK,GAAG,cAAc;sBAAA;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjE,OAAA;kBAAK6D,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB5D,OAAA;oBAAK6D,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,EAC9ClE,cAAc,CAACsD,OAAO,CAAC7B,KAAK;kBAAC;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNjE,OAAA;oBAAK6D,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,GAAC,QAC/B,EAACZ,OAAO,CAAC8B,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAC,OACnC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAjCEjB,OAAO,CAACS,EAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkCf,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAvD,eAAe,iBACdV,OAAA;QAAK6D,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F5D,OAAA;UAAK6D,SAAS,EAAC,yCAAyC;UAAAD,QAAA,gBACtD5D,OAAA;YAAI6D,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EjE,OAAA;YAAM+E,QAAQ,EAAEjD,mBAAoB;YAAA8B,QAAA,gBAClC5D,OAAA;cAAK6D,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxB5D,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAOgF,OAAO,EAAC,OAAO;kBAACnB,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjE,OAAA;kBACEyD,EAAE,EAAC,OAAO;kBACVW,KAAK,EAAEpD,QAAQ,CAACE,QAAS;kBACzBmD,QAAQ,EAAGtC,CAAC,IAAKd,WAAW,CAAC;oBAAC,GAAGD,QAAQ;oBAAEE,QAAQ,EAAEa,CAAC,CAACuC,MAAM,CAACF;kBAAK,CAAC,CAAE;kBACtEP,SAAS,EAAC,aAAa;kBACvBoB,QAAQ;kBAAArB,QAAA,gBAER5D,OAAA;oBAAQoE,KAAK,EAAC,EAAE;oBAAAR,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvC7D,MAAM,CAACmE,GAAG,CAACC,KAAK,iBACfxE,OAAA;oBAAuBoE,KAAK,EAAEI,KAAK,CAACf,EAAG;oBAAAG,QAAA,EAAEY,KAAK,CAACjB;kBAAI,GAAtCiB,KAAK,CAACf,EAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENjE,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAOgF,OAAO,EAAC,aAAa;kBAACnB,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEtF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjE,OAAA;kBACEyD,EAAE,EAAC,aAAa;kBAChBS,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAEpD,QAAQ,CAACI,WAAY;kBAC5BiD,QAAQ,EAAGtC,CAAC,IAAKd,WAAW,CAAC;oBAAC,GAAGD,QAAQ;oBAAEI,WAAW,EAAEW,CAAC,CAACuC,MAAM,CAACF;kBAAK,CAAC,CAAE;kBACzED,WAAW,EAAC,4BAA4B;kBACxCN,SAAS,EAAC,aAAa;kBACvBoB,QAAQ;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAOgF,OAAO,EAAC,QAAQ;kBAACnB,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEjF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjE,OAAA;kBAAK6D,SAAS,EAAC,UAAU;kBAAAD,QAAA,gBACvB5D,OAAA,CAACd,UAAU;oBAAC2E,SAAS,EAAC;kBAA0E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnGjE,OAAA;oBACEyD,EAAE,EAAC,QAAQ;oBACXS,IAAI,EAAC,QAAQ;oBACbgB,IAAI,EAAC,MAAM;oBACXC,GAAG,EAAC,GAAG;oBACPf,KAAK,EAAEpD,QAAQ,CAACG,KAAM;oBACtBkD,QAAQ,EAAGtC,CAAC,IAAKd,WAAW,CAAC;sBAAC,GAAGD,QAAQ;sBAAEG,KAAK,EAAEY,CAAC,CAACuC,MAAM,CAACF;oBAAK,CAAC,CAAE;oBACnED,WAAW,EAAC,MAAM;oBAClBN,SAAS,EAAC,mBAAmB;oBAC7BoB,QAAQ;kBAAA;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjE,OAAA;cAAK6D,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC9B5D,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbO,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,KAAK,CAAE;gBACzCkD,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACjC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA;gBAAQkE,IAAI,EAAC,QAAQ;gBAACL,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC/D,EAAA,CAtRID,QAAkB;EAAA,QACLR,OAAO,EAqCxBG,cAAc;AAAA;AAAAwF,EAAA,GAtCVnF,QAAkB;AAwRxB,eAAeA,QAAQ;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}