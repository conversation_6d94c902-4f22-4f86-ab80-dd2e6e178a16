"""
Enhanced group management service with owner controls, join requests, and member management
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional
from datetime import datetime

from ..models import User, Group, GroupJoinRequest, Expense, Share, JoinRequestStatus
from .. import schemas


class GroupManagementService:
    def __init__(self, db: Session):
        self.db = db

    def create_group(self, creator: User, name: str, description: str = None) -> Group:
        """Create a new group with the creator as the owner"""
        group = Group(
            name=name,
            description=description,
            creator_id=creator.id
        )
        
        self.db.add(group)
        self.db.commit()
        self.db.refresh(group)
        
        # Add creator as a member
        group.members.append(creator)
        self.db.commit()
        
        return group

    def request_to_join_group(self, user: User, group_id: int, message: str = None) -> GroupJoinRequest:
        """Create a join request for a group"""
        # Check if user is already a member
        group = self.db.query(Group).filter(Group.id == group_id).first()
        if not group:
            raise ValueError("Group not found")
        
        if user in group.members:
            raise ValueError("User is already a member of this group")
        
        # Check if there's already a pending request
        existing_request = self.db.query(GroupJoinRequest).filter(
            and_(
                GroupJoinRequest.group_id == group_id,
                GroupJoinRequest.user_id == user.id,
                GroupJoinRequest.status == JoinRequestStatus.PENDING.value
            )
        ).first()
        
        if existing_request:
            raise ValueError("Join request already pending")
        
        join_request = GroupJoinRequest(
            group_id=group_id,
            user_id=user.id,
            message=message,
            status=JoinRequestStatus.PENDING.value
        )
        
        self.db.add(join_request)
        self.db.commit()
        self.db.refresh(join_request)
        
        return join_request

    def process_join_request(self, processor: User, request_id: int, approved: bool) -> GroupJoinRequest:
        """Approve or reject a join request (only group owner can do this)"""
        join_request = self.db.query(GroupJoinRequest).filter(GroupJoinRequest.id == request_id).first()
        if not join_request:
            raise ValueError("Join request not found")
        
        group = join_request.group
        if group.creator_id != processor.id:
            raise ValueError("Only the group owner can process join requests")
        
        if join_request.status != JoinRequestStatus.PENDING.value:
            raise ValueError("Join request has already been processed")
        
        join_request.status = JoinRequestStatus.APPROVED.value if approved else JoinRequestStatus.REJECTED.value
        join_request.processed_at = datetime.utcnow()
        join_request.processed_by = processor.id
        
        # If approved, add user to group
        if approved:
            user = join_request.user
            group.members.append(user)
        
        self.db.commit()
        return join_request

    def get_pending_join_requests(self, group_owner: User, group_id: int = None) -> List[GroupJoinRequest]:
        """Get pending join requests for groups owned by the user"""
        query = self.db.query(GroupJoinRequest).join(Group).filter(
            and_(
                Group.creator_id == group_owner.id,
                GroupJoinRequest.status == JoinRequestStatus.PENDING.value
            )
        )
        
        if group_id:
            query = query.filter(GroupJoinRequest.group_id == group_id)
        
        return query.all()

    def remove_member(self, group_owner: User, group_id: int, user_id: int) -> dict:
        """Remove a member from the group (only owner can do this)"""
        group = self.db.query(Group).filter(Group.id == group_id).first()
        if not group:
            raise ValueError("Group not found")
        
        if group.creator_id != group_owner.id:
            raise ValueError("Only the group owner can remove members")
        
        if user_id == group_owner.id:
            raise ValueError("Group owner cannot remove themselves")
        
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError("User not found")
        
        # Check membership by user ID instead of object comparison
        member_ids = [member.id for member in group.members]
        if user.id not in member_ids:
            raise ValueError("User is not a member of this group")
        
        # Check if user has unsettled debts
        unsettled_balance = self._get_user_balance_in_group(user_id, group_id)
        if abs(unsettled_balance) > 0.01:  # Allow for small rounding errors
            raise ValueError(f"Cannot remove user with unsettled balance: ${abs(unsettled_balance):.2f}")
        
        # Remove user from group members using proper SQLAlchemy method
        group.members = [member for member in group.members if member.id != user.id]
        self.db.commit()
        
        return {
            "message": f"User {user.email} removed from group {group.name}",
            "group_id": group_id,
            "removed_user_id": user_id
        }

    def leave_group(self, user: User, group_id: int) -> dict:
        """Allow a user to leave a group"""
        group = self.db.query(Group).filter(Group.id == group_id).first()
        if not group:
            raise ValueError("Group not found")
        
        # Check membership by user ID instead of object comparison
        member_ids = [member.id for member in group.members]
        if user.id not in member_ids:
            raise ValueError("User is not a member of this group")
        
        if group.creator_id == user.id:
            raise ValueError("Group owner cannot leave the group. Transfer ownership first.")
        
        # Check if user has unsettled debts
        unsettled_balance = self._get_user_balance_in_group(user.id, group_id)
        if abs(unsettled_balance) > 0.01:  # Allow for small rounding errors
            raise ValueError(f"Cannot leave group with unsettled balance: ${abs(unsettled_balance):.2f}")
        
        # Remove user from group members using proper SQLAlchemy method
        group.members = [member for member in group.members if member.id != user.id]
        self.db.commit()
        
        return {
            "message": f"You have left the group {group.name}",
            "group_id": group_id
        }

    def transfer_ownership(self, current_owner: User, group_id: int, new_owner_id: int) -> dict:
        """Transfer group ownership to another member"""
        group = self.db.query(Group).filter(Group.id == group_id).first()
        if not group:
            raise ValueError("Group not found")
        
        if group.creator_id != current_owner.id:
            raise ValueError("Only the current owner can transfer ownership")
        
        new_owner = self.db.query(User).filter(User.id == new_owner_id).first()
        if not new_owner:
            raise ValueError("New owner not found")
        
        if new_owner not in group.members:
            raise ValueError("New owner must be a member of the group")
        
        group.creator_id = new_owner_id
        self.db.commit()
        
        return {
            "message": f"Group ownership transferred to {new_owner.email}",
            "group_id": group_id,
            "new_owner_id": new_owner_id
        }

    def update_group(self, owner: User, group_id: int, name: str = None, description: str = None) -> Group:
        """Update group details (only owner can do this)"""
        group = self.db.query(Group).filter(Group.id == group_id).first()
        if not group:
            raise ValueError("Group not found")
        
        if group.creator_id != owner.id:
            raise ValueError("Only the group owner can update group details")
        
        if name:
            group.name = name
        if description is not None:
            group.description = description
        
        self.db.commit()
        self.db.refresh(group)
        
        return group

    def get_group_details(self, user: User, group_id: int) -> dict:
        """Get detailed group information including member balances"""
        group = self.db.query(Group).filter(Group.id == group_id).first()
        if not group:
            raise ValueError("Group not found")

        # Check membership by user ID instead of object comparison
        member_ids = [member.id for member in group.members]
        if user.id not in member_ids:
            raise ValueError("User is not a member of this group")
        
        # Get member balances within this group
        member_balances = []
        for member in group.members:
            balance = self._get_user_balance_in_group(member.id, group_id)
            member_balances.append({
                "user_id": member.id,
                "email": member.email,
                "balance": balance
            })
        
        return {
            "id": group.id,
            "name": group.name,
            "description": group.description,
            "creator_id": group.creator_id,
            "creator_email": group.creator.email,
            "created_at": group.created_at.isoformat() if group.created_at else None,
            "member_count": len(group.members),
            "members": member_balances,
            "is_owner": group.creator_id == user.id
        }

    def _get_user_balance_in_group(self, user_id: int, group_id: int) -> float:
        """Calculate user's balance within a specific group"""
        from ..models import Settlement

        # Get all approved expenses in this group
        expenses = self.db.query(Expense).filter(
            and_(
                Expense.group_id == group_id,
                Expense.status == "approved"
            )
        ).all()

        balance = 0.0

        for expense in expenses:
            if expense.payer_id == user_id:
                # User paid for this expense - they are owed the sum of unpaid shares
                unpaid_shares = [s for s in expense.shares if not s.paid]
                balance += sum(float(share.amount) for share in unpaid_shares)
            else:
                # Check if user has a share in this expense
                user_share = next((s for s in expense.shares if s.user_id == user_id), None)
                if user_share and not user_share.paid:
                    balance -= float(user_share.amount)  # They owe this amount

        # Account for settlements within this group
        # Get all confirmed settlements where this user was involved
        settlements_sent = self.db.query(Settlement).filter(
            and_(
                Settlement.payer_id == user_id,
                Settlement.status == "confirmed"
            )
        ).all()

        settlements_received = self.db.query(Settlement).filter(
            and_(
                Settlement.recipient_id == user_id,
                Settlement.status == "confirmed"
            )
        ).all()

        # Subtract money sent in settlements
        for settlement in settlements_sent:
            # Check if this settlement is related to this group by checking if recipient is a group member
            group = self.db.query(Group).filter(Group.id == group_id).first()
            if group and any(member.id == settlement.recipient_id for member in group.members):
                balance -= float(settlement.amount)

        # Add money received in settlements
        for settlement in settlements_received:
            # Check if this settlement is related to this group by checking if payer is a group member
            group = self.db.query(Group).filter(Group.id == group_id).first()
            if group and any(member.id == settlement.payer_id for member in group.members):
                balance += float(settlement.amount)

        return balance
