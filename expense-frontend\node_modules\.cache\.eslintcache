[{"C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Dashboard.tsx": "3", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\ProtectedRoute.tsx": "5", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\LoginForm.tsx": "6", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\AIChat.tsx": "7", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Layout.tsx": "10", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Header.tsx": "11", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Sidebar.tsx": "12", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Groups.tsx": "13", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settlements.tsx": "14", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Expenses.tsx": "15", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\utils\\formatters.ts": "16", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Approvals.tsx": "17", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\SettlementConfirmations.tsx": "18", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\notificationService.ts": "19", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\hooks\\useAutoRefresh.ts": "20", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\GroupManagement\\GroupManagementModal.tsx": "21", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settings.tsx": "22", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\ConfirmationDialog.tsx": "23"}, {"size": 274, "mtime": 1753437234701, "results": "24", "hashOfConfig": "25"}, {"size": 3264, "mtime": 1753463453810, "results": "26", "hashOfConfig": "25"}, {"size": 8531, "mtime": 1753451701664, "results": "27", "hashOfConfig": "25"}, {"size": 3064, "mtime": 1753436944528, "results": "28", "hashOfConfig": "25"}, {"size": 682, "mtime": 1753437181945, "results": "29", "hashOfConfig": "25"}, {"size": 4147, "mtime": 1753437003408, "results": "30", "hashOfConfig": "25"}, {"size": 8571, "mtime": 1753437172037, "results": "31", "hashOfConfig": "25"}, {"size": 5838, "mtime": 1753437038296, "results": "32", "hashOfConfig": "25"}, {"size": 6813, "mtime": 1753463295099, "results": "33", "hashOfConfig": "25"}, {"size": 777, "mtime": 1753436985760, "results": "34", "hashOfConfig": "25"}, {"size": 6260, "mtime": 1753451616428, "results": "35", "hashOfConfig": "25"}, {"size": 2988, "mtime": 1753465076986, "results": "36", "hashOfConfig": "25"}, {"size": 16101, "mtime": 1753463274513, "results": "37", "hashOfConfig": "25"}, {"size": 14757, "mtime": 1753456043289, "results": "38", "hashOfConfig": "25"}, {"size": 11815, "mtime": 1753456004382, "results": "39", "hashOfConfig": "25"}, {"size": 1713, "mtime": 1753444405103, "results": "40", "hashOfConfig": "25"}, {"size": 12373, "mtime": 1753456131120, "results": "41", "hashOfConfig": "25"}, {"size": 14285, "mtime": 1753462462527, "results": "42", "hashOfConfig": "25"}, {"size": 6225, "mtime": 1753462494531, "results": "43", "hashOfConfig": "25"}, {"size": 1029, "mtime": 1753451931063, "results": "44", "hashOfConfig": "25"}, {"size": 27497, "mtime": 1753465697348, "results": "45", "hashOfConfig": "25"}, {"size": 36914, "mtime": 1753463413844, "results": "46", "hashOfConfig": "25"}, {"size": 4586, "mtime": 1753464280031, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11ylyi8", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\AIChat.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Groups.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settlements.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Expenses.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\utils\\formatters.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Approvals.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\SettlementConfirmations.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\notificationService.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\hooks\\useAutoRefresh.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\GroupManagement\\GroupManagementModal.tsx", ["117"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settings.tsx", ["118", "119", "120", "121"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\ConfirmationDialog.tsx", [], [], {"ruleId": "122", "severity": 1, "message": "123", "line": 109, "column": 6, "nodeType": "124", "endLine": 109, "endColumn": 23, "suggestions": "125"}, {"ruleId": "126", "severity": 1, "message": "127", "line": 20, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 20, "endColumn": 14}, {"ruleId": "126", "severity": 1, "message": "130", "line": 21, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 21, "endColumn": 7}, {"ruleId": "126", "severity": 1, "message": "131", "line": 22, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 22, "endColumn": 6}, {"ruleId": "126", "severity": 1, "message": "132", "line": 23, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 23, "endColumn": 13}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'checkCanLeave', 'isOwner', 'loadGroupDetails', and 'loadJoinRequests'. Either include them or remove the dependency array.", "ArrayExpression", ["133"], "@typescript-eslint/no-unused-vars", "'CheckCircle' is defined but never used.", "Identifier", "unusedVar", "'Moon' is defined but never used.", "'Sun' is defined but never used.", "'DollarSign' is defined but never used.", {"desc": "134", "fix": "135"}, "Update the dependencies array to be: [isOpen, groupId, loadGroupDetails, isOwner, checkCanLeave, loadJoinRequests]", {"range": "136", "text": "137"}, [2691, 2708], "[isOpen, groupId, loadGroupDetails, isOwner, checkCanLeave, loadJoinRequests]"]