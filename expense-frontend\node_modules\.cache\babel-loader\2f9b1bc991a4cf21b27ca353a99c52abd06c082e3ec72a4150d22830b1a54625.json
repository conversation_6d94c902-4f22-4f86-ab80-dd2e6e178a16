{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\pages\\\\Groups.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { Plus, Users, UserPlus, Settings, Crown, Search, X } from 'lucide-react';\nimport { groupsAPI, groupManagementAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport GroupManagementModal from '../components/GroupManagement/GroupManagementModal';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Groups = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [groups, setGroups] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showJoinModal, setShowJoinModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showManagementModal, setShowManagementModal] = useState(false);\n  const [selectedGroup, setSelectedGroup] = useState(null);\n  const [newGroupName, setNewGroupName] = useState('');\n  const [joinGroupId, setJoinGroupId] = useState('');\n  const [joinMessage, setJoinMessage] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  useEffect(() => {\n    loadGroups();\n  }, []);\n  const loadGroups = async () => {\n    try {\n      const response = await groupsAPI.getMyGroups();\n      setGroups(response.data);\n    } catch (error) {\n      toast.error('Failed to load groups');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateGroup = async e => {\n    e.preventDefault();\n    if (!newGroupName.trim()) return;\n    try {\n      const response = await groupsAPI.createGroup({\n        name: newGroupName\n      });\n      setGroups([...groups, response.data]);\n      setNewGroupName('');\n      setShowCreateModal(false);\n      toast.success('Group created successfully!');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Failed to create group');\n    }\n  };\n  const handleJoinGroup = async e => {\n    e.preventDefault();\n    if (!joinGroupId.trim()) return;\n    try {\n      const response = await groupManagementAPI.requestToJoin(parseInt(joinGroupId), joinMessage.trim() || \"I'd like to join this group\");\n      setJoinGroupId('');\n      setJoinMessage('');\n      setShowJoinModal(false);\n      toast.success('Join request sent successfully! The group owner will review your request.');\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Failed to send join request');\n    }\n  };\n  const handleViewDetails = groupId => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowDetailsModal(true);\n    }\n  };\n  const handleManageGroup = groupId => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowManagementModal(true);\n    }\n  };\n  const handleGroupUpdated = () => {\n    loadGroups(); // Refresh the groups list\n  };\n  const filteredGroups = groups.filter(group => group.name.toLowerCase().includes(searchTerm.toLowerCase()));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"Groups\",\n      subtitle: \"Manage your expense sharing groups\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"Groups\",\n    subtitle: \"Manage your expense sharing groups\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search groups...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowJoinModal(true),\n            className: \"btn-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), \"Request to Join\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCreateModal(true),\n            className: \"btn-primary\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), \"Create Group\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), filteredGroups.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: searchTerm ? 'No groups found' : 'No groups yet'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-6\",\n          children: searchTerm ? 'Try adjusting your search terms' : 'Create your first group or join an existing one to start sharing expenses'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), !searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateModal(true),\n          className: \"btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 17\n          }, this), \"Create Your First Group\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: filteredGroups.map(group => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card hover:shadow-lg transition-shadow\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-6 h-6 text-primary-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900\",\n                      children: group.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 27\n                    }, this), group.creator_id === (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                      children: [/*#__PURE__*/_jsxDEV(Crown, {\n                        className: \"w-3 h-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 31\n                      }, this), \"Owner\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"ID: \", group.id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleManageGroup(group.id),\n                className: \"btn-ghost p-2\",\n                title: \"Manage Group\",\n                children: /*#__PURE__*/_jsxDEV(Settings, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: group.members.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [group.members.slice(0, 3).map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-gray-600\",\n                      children: member.email.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm text-gray-700 truncate\",\n                    children: member.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 27\n                  }, this), member.id === group.creator_id && /*#__PURE__*/_jsxDEV(Crown, {\n                    className: \"w-3 h-3 text-yellow-500 ml-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 29\n                  }, this)]\n                }, member.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 25\n                }, this)), group.members.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"+\", group.members.length - 3, \" more members\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 pt-4 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleViewDetails(group.id),\n                className: \"w-full btn-ghost text-sm\",\n                children: \"View Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 17\n          }, this)\n        }, group.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), showCreateModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Create New Group\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreateGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"groupName\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Group Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"groupName\",\n                type: \"text\",\n                value: newGroupName,\n                onChange: e => setNewGroupName(e.target.value),\n                placeholder: \"Enter group name (e.g., Office Team, Roommates)\",\n                className: \"input-field\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowCreateModal(false),\n                className: \"flex-1 btn-secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"flex-1 btn-primary\",\n                children: \"Create Group\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), showJoinModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Request to Join Group\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-4\",\n            children: \"Send a join request to the group owner. They will review and approve your request.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleJoinGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"groupId\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Group ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"groupId\",\n                type: \"number\",\n                value: joinGroupId,\n                onChange: e => setJoinGroupId(e.target.value),\n                placeholder: \"Enter the group ID to join\",\n                className: \"input-field\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"Ask a group member for the group ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"joinMessage\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Message (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"joinMessage\",\n                value: joinMessage,\n                onChange: e => setJoinMessage(e.target.value),\n                placeholder: \"Tell the group owner why you'd like to join...\",\n                className: \"input-field resize-none\",\n                rows: 3,\n                maxLength: 200\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: [joinMessage.length, \"/200 characters\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setShowJoinModal(false);\n                  setJoinGroupId('');\n                  setJoinMessage('');\n                },\n                className: \"flex-1 btn-secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"flex-1 btn-primary\",\n                children: \"Send Request\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this), showDetailsModal && selectedGroup && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: selectedGroup.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDetailsModal(false),\n              className: \"text-gray-400 hover:text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-2\",\n                children: \"Group Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Group ID:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: selectedGroup.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Members:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: selectedGroup.members.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-3\",\n                children: \"Members\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: selectedGroup.members.map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-primary-600\",\n                        children: member.email.charAt(0).toUpperCase()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: member.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 29\n                      }, this), member.id === selectedGroup.creator_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-yellow-600 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Crown, {\n                          className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 396,\n                          columnNumber: 33\n                        }, this), \"Group Owner\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 395,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 25\n                  }, this)\n                }, member.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-3 pt-4 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowDetailsModal(false),\n                className: \"btn-secondary\",\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this), showManagementModal && selectedGroup && /*#__PURE__*/_jsxDEV(GroupManagementModal, {\n        isOpen: showManagementModal,\n        onClose: () => setShowManagementModal(false),\n        groupId: selectedGroup.id,\n        groupName: selectedGroup.name,\n        isOwner: selectedGroup.creator_id === (user === null || user === void 0 ? void 0 : user.id),\n        onGroupUpdated: handleGroupUpdated\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(Groups, \"Gtk0Ur4K0LXIHTE5dS0KEoe12gw=\", false, function () {\n  return [useAuth];\n});\n_c = Groups;\nexport default Groups;\nvar _c;\n$RefreshReg$(_c, \"Groups\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "Plus", "Users", "UserPlus", "Settings", "Crown", "Search", "X", "groupsAPI", "groupManagementAPI", "useAuth", "GroupManagementModal", "toast", "jsxDEV", "_jsxDEV", "Groups", "_s", "user", "groups", "setGroups", "loading", "setLoading", "showCreateModal", "setShowCreateModal", "showJoinModal", "setShowJoinModal", "showDetailsModal", "setShowDetailsModal", "showManagementModal", "setShowManagementModal", "selectedGroup", "setSelectedGroup", "newGroupName", "setNewGroupName", "joinGroupId", "setJoinGroupId", "joinMessage", "setJoinMessage", "searchTerm", "setSearchTerm", "loadGroups", "response", "getMyGroups", "data", "error", "handleCreateGroup", "e", "preventDefault", "trim", "createGroup", "name", "success", "_error$response", "_error$response$data", "detail", "handleJoinGroup", "requestToJoin", "parseInt", "_error$response2", "_error$response2$data", "handleViewDetails", "groupId", "group", "find", "g", "id", "handleManageGroup", "handleGroupUpdated", "filteredGroups", "filter", "toLowerCase", "includes", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onClick", "length", "map", "creator_id", "members", "slice", "member", "email", "char<PERSON>t", "toUpperCase", "onSubmit", "htmlFor", "required", "rows", "max<PERSON><PERSON><PERSON>", "isOpen", "onClose", "groupName", "isOwner", "onGroupUpdated", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/pages/Groups.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport {\n  Plus,\n  Users,\n  UserPlus,\n  Settings,\n\n  Crown,\n  Search,\n  X\n} from 'lucide-react';\nimport { Group } from '../types';\nimport { groupsAPI, groupManagementAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport GroupManagementModal from '../components/GroupManagement/GroupManagementModal';\nimport toast from 'react-hot-toast';\n\nconst Groups: React.FC = () => {\n  const { user } = useAuth();\n  const [groups, setGroups] = useState<Group[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showJoinModal, setShowJoinModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showManagementModal, setShowManagementModal] = useState(false);\n  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);\n  const [newGroupName, setNewGroupName] = useState('');\n  const [joinGroupId, setJoinGroupId] = useState('');\n  const [joinMessage, setJoinMessage] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    loadGroups();\n  }, []);\n\n  const loadGroups = async () => {\n    try {\n      const response = await groupsAPI.getMyGroups();\n      setGroups(response.data);\n    } catch (error) {\n      toast.error('Failed to load groups');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateGroup = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newGroupName.trim()) return;\n\n    try {\n      const response = await groupsAPI.createGroup({ name: newGroupName });\n      setGroups([...groups, response.data]);\n      setNewGroupName('');\n      setShowCreateModal(false);\n      toast.success('Group created successfully!');\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to create group');\n    }\n  };\n\n  const handleJoinGroup = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!joinGroupId.trim()) return;\n\n    try {\n      const response = await groupManagementAPI.requestToJoin(\n        parseInt(joinGroupId),\n        joinMessage.trim() || \"I'd like to join this group\"\n      );\n\n      setJoinGroupId('');\n      setJoinMessage('');\n      setShowJoinModal(false);\n      toast.success('Join request sent successfully! The group owner will review your request.');\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to send join request');\n    }\n  };\n\n  const handleViewDetails = (groupId: number) => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowDetailsModal(true);\n    }\n  };\n\n  const handleManageGroup = (groupId: number) => {\n    const group = groups.find(g => g.id === groupId);\n    if (group) {\n      setSelectedGroup(group);\n      setShowManagementModal(true);\n    }\n  };\n\n  const handleGroupUpdated = () => {\n    loadGroups(); // Refresh the groups list\n  };\n\n  const filteredGroups = groups.filter(group =>\n    group.name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) {\n    return (\n      <Layout title=\"Groups\" subtitle=\"Manage your expense sharing groups\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout title=\"Groups\" subtitle=\"Manage your expense sharing groups\">\n      <div className=\"space-y-6\">\n        {/* Header Actions */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search groups...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64\"\n            />\n          </div>\n          \n          <div className=\"flex gap-3\">\n            <button\n              onClick={() => setShowJoinModal(true)}\n              className=\"btn-secondary\"\n            >\n              <UserPlus className=\"w-4 h-4 mr-2\" />\n              Request to Join\n            </button>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"btn-primary\"\n            >\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Create Group\n            </button>\n          </div>\n        </div>\n\n        {/* Groups Grid */}\n        {filteredGroups.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Users className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {searchTerm ? 'No groups found' : 'No groups yet'}\n            </h3>\n            <p className=\"text-gray-500 mb-6\">\n              {searchTerm \n                ? 'Try adjusting your search terms'\n                : 'Create your first group or join an existing one to start sharing expenses'\n              }\n            </p>\n            {!searchTerm && (\n              <button\n                onClick={() => setShowCreateModal(true)}\n                className=\"btn-primary\"\n              >\n                <Plus className=\"w-4 h-4 mr-2\" />\n                Create Your First Group\n              </button>\n            )}\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredGroups.map((group) => (\n              <div key={group.id} className=\"card hover:shadow-lg transition-shadow\">\n                <div className=\"card-content\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\">\n                        <Users className=\"w-6 h-6 text-primary-600\" />\n                      </div>\n                      <div className=\"ml-3\">\n                        <div className=\"flex items-center space-x-2\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">{group.name}</h3>\n                          {group.creator_id === user?.id && (\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                              <Crown className=\"w-3 h-3 mr-1\" />\n                              Owner\n                            </span>\n                          )}\n                        </div>\n                        <p className=\"text-sm text-gray-500\">ID: {group.id}</p>\n                      </div>\n                    </div>\n                    <button\n                      onClick={() => handleManageGroup(group.id)}\n                      className=\"btn-ghost p-2\"\n                      title=\"Manage Group\"\n                    >\n                      <Settings className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  \n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-gray-600\">Members</span>\n                      <span className=\"font-medium\">{group.members.length}</span>\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      {group.members.slice(0, 3).map((member) => (\n                        <div key={member.id} className=\"flex items-center\">\n                          <div className=\"w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-xs font-medium text-gray-600\">\n                              {member.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <span className=\"ml-2 text-sm text-gray-700 truncate\">\n                            {member.email}\n                          </span>\n                          {member.id === group.creator_id && (\n                            <Crown className=\"w-3 h-3 text-yellow-500 ml-1\" />\n                          )}\n                        </div>\n                      ))}\n                      {group.members.length > 3 && (\n                        <div className=\"text-xs text-gray-500\">\n                          +{group.members.length - 3} more members\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                    <button\n                      onClick={() => handleViewDetails(group.id)}\n                      className=\"w-full btn-ghost text-sm\"\n                    >\n                      View Details\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Create Group Modal */}\n        {showCreateModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Create New Group</h3>\n              <form onSubmit={handleCreateGroup}>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"groupName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Group Name\n                  </label>\n                  <input\n                    id=\"groupName\"\n                    type=\"text\"\n                    value={newGroupName}\n                    onChange={(e) => setNewGroupName(e.target.value)}\n                    placeholder=\"Enter group name (e.g., Office Team, Roommates)\"\n                    className=\"input-field\"\n                    required\n                  />\n                </div>\n                <div className=\"flex gap-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateModal(false)}\n                    className=\"flex-1 btn-secondary\"\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"flex-1 btn-primary\">\n                    Create Group\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Join Group Modal */}\n        {showJoinModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Request to Join Group</h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Send a join request to the group owner. They will review and approve your request.\n              </p>\n              <form onSubmit={handleJoinGroup}>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"groupId\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Group ID\n                  </label>\n                  <input\n                    id=\"groupId\"\n                    type=\"number\"\n                    value={joinGroupId}\n                    onChange={(e) => setJoinGroupId(e.target.value)}\n                    placeholder=\"Enter the group ID to join\"\n                    className=\"input-field\"\n                    required\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    Ask a group member for the group ID\n                  </p>\n                </div>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"joinMessage\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Message (Optional)\n                  </label>\n                  <textarea\n                    id=\"joinMessage\"\n                    value={joinMessage}\n                    onChange={(e) => setJoinMessage(e.target.value)}\n                    placeholder=\"Tell the group owner why you'd like to join...\"\n                    className=\"input-field resize-none\"\n                    rows={3}\n                    maxLength={200}\n                  />\n                  <p className=\"mt-1 text-xs text-gray-500\">\n                    {joinMessage.length}/200 characters\n                  </p>\n                </div>\n                <div className=\"flex gap-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowJoinModal(false);\n                      setJoinGroupId('');\n                      setJoinMessage('');\n                    }}\n                    className=\"flex-1 btn-secondary\"\n                  >\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"flex-1 btn-primary\">\n                    Send Request\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Group Details Modal */}\n        {showDetailsModal && selectedGroup && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold text-gray-900\">{selectedGroup.name}</h3>\n                <button\n                  onClick={() => setShowDetailsModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n              </div>\n\n              <div className=\"space-y-6\">\n                {/* Group Info */}\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Group Information</h4>\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600\">Group ID:</span>\n                      <span className=\"ml-2 font-medium\">{selectedGroup.id}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">Members:</span>\n                      <span className=\"ml-2 font-medium\">{selectedGroup.members.length}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Members List */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Members</h4>\n                  <div className=\"space-y-2\">\n                    {selectedGroup.members.map((member) => (\n                      <div key={member.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-medium text-primary-600\">\n                              {member.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className=\"text-sm font-medium text-gray-900\">{member.email}</p>\n                            {member.id === selectedGroup.creator_id && (\n                              <p className=\"text-xs text-yellow-600 flex items-center\">\n                                <Crown className=\"w-3 h-3 mr-1\" />\n                                Group Owner\n                              </p>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Actions */}\n                <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n                  <button\n                    onClick={() => setShowDetailsModal(false)}\n                    className=\"btn-secondary\"\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Group Management Modal */}\n        {showManagementModal && selectedGroup && (\n          <GroupManagementModal\n            isOpen={showManagementModal}\n            onClose={() => setShowManagementModal(false)}\n            groupId={selectedGroup.id}\n            groupName={selectedGroup.name}\n            isOwner={selectedGroup.creator_id === user?.id}\n            onGroupUpdated={handleGroupUpdated}\n          />\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Groups;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SACEC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EAERC,KAAK,EACLC,MAAM,EACNC,CAAC,QACI,cAAc;AAErB,SAASC,SAAS,EAAEC,kBAAkB,QAAQ,iBAAiB;AAC/D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdyC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjC,SAAS,CAACkC,WAAW,CAAC,CAAC;MAC9CvB,SAAS,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhC,KAAK,CAACgC,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,iBAAiB,GAAG,MAAOC,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACf,YAAY,CAACgB,IAAI,CAAC,CAAC,EAAE;IAE1B,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMjC,SAAS,CAACyC,WAAW,CAAC;QAAEC,IAAI,EAAElB;MAAa,CAAC,CAAC;MACpEb,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEuB,QAAQ,CAACE,IAAI,CAAC,CAAC;MACrCV,eAAe,CAAC,EAAE,CAAC;MACnBV,kBAAkB,CAAC,KAAK,CAAC;MACzBX,KAAK,CAACuC,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAAQ,eAAA,EAAAC,oBAAA;MACnBzC,KAAK,CAACgC,KAAK,CAAC,EAAAQ,eAAA,GAAAR,KAAK,CAACH,QAAQ,cAAAW,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBT,IAAI,cAAAU,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,wBAAwB,CAAC;IACvE;EACF,CAAC;EAED,MAAMC,eAAe,GAAG,MAAOT,CAAkB,IAAK;IACpDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACb,WAAW,CAACc,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMhC,kBAAkB,CAAC+C,aAAa,CACrDC,QAAQ,CAACvB,WAAW,CAAC,EACrBE,WAAW,CAACY,IAAI,CAAC,CAAC,IAAI,6BACxB,CAAC;MAEDb,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,EAAE,CAAC;MAClBZ,gBAAgB,CAAC,KAAK,CAAC;MACvBb,KAAK,CAACuC,OAAO,CAAC,2EAA2E,CAAC;IAC5F,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACnB/C,KAAK,CAACgC,KAAK,CAAC,EAAAc,gBAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,6BAA6B,CAAC;IAC5E;EACF,CAAC;EAED,MAAMM,iBAAiB,GAAIC,OAAe,IAAK;IAC7C,MAAMC,KAAK,GAAG5C,MAAM,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;IAChD,IAAIC,KAAK,EAAE;MACT/B,gBAAgB,CAAC+B,KAAK,CAAC;MACvBnC,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMuC,iBAAiB,GAAIL,OAAe,IAAK;IAC7C,MAAMC,KAAK,GAAG5C,MAAM,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;IAChD,IAAIC,KAAK,EAAE;MACT/B,gBAAgB,CAAC+B,KAAK,CAAC;MACvBjC,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B3B,UAAU,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAM4B,cAAc,GAAGlD,MAAM,CAACmD,MAAM,CAACP,KAAK,IACxCA,KAAK,CAACZ,IAAI,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAC5D,CAAC;EAED,IAAIlD,OAAO,EAAE;IACX,oBACEN,OAAA,CAACd,MAAM;MAACwE,KAAK,EAAC,QAAQ;MAACC,QAAQ,EAAC,oCAAoC;MAAAC,QAAA,eAClE5D,OAAA;QAAK6D,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpD5D,OAAA;UAAK6D,SAAS,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACEjE,OAAA,CAACd,MAAM;IAACwE,KAAK,EAAC,QAAQ;IAACC,QAAQ,EAAC,oCAAoC;IAAAC,QAAA,eAClE5D,OAAA;MAAK6D,SAAS,EAAC,WAAW;MAAAD,QAAA,gBAExB5D,OAAA;QAAK6D,SAAS,EAAC,oEAAoE;QAAAD,QAAA,gBACjF5D,OAAA;UAAK6D,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB5D,OAAA,CAACR,MAAM;YAACqE,SAAS,EAAC;UAA0E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FjE,OAAA;YACEkE,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kBAAkB;YAC9BC,KAAK,EAAE5C,UAAW;YAClB6C,QAAQ,EAAGrC,CAAC,IAAKP,aAAa,CAACO,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;YAC/CP,SAAS,EAAC;UAAwI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjE,OAAA;UAAK6D,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB5D,OAAA;YACEuE,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAAC,IAAI,CAAE;YACtCkD,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAEzB5D,OAAA,CAACX,QAAQ;cAACwE,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA;YACEuE,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,IAAI,CAAE;YACxCoD,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAEvB5D,OAAA,CAACb,IAAI;cAAC0E,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLX,cAAc,CAACkB,MAAM,KAAK,CAAC,gBAC1BxE,OAAA;QAAK6D,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChC5D,OAAA,CAACZ,KAAK;UAACyE,SAAS,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DjE,OAAA;UAAI6D,SAAS,EAAC,wCAAwC;UAAAD,QAAA,EACnDpC,UAAU,GAAG,iBAAiB,GAAG;QAAe;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACLjE,OAAA;UAAG6D,SAAS,EAAC,oBAAoB;UAAAD,QAAA,EAC9BpC,UAAU,GACP,iCAAiC,GACjC;QAA2E;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE9E,CAAC,EACH,CAACzC,UAAU,iBACVxB,OAAA;UACEuE,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,IAAI,CAAE;UACxCoD,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAEvB5D,OAAA,CAACb,IAAI;YAAC0E,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENjE,OAAA;QAAK6D,SAAS,EAAC,sDAAsD;QAAAD,QAAA,EAClEN,cAAc,CAACmB,GAAG,CAAEzB,KAAK,iBACxBhD,OAAA;UAAoB6D,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eACpE5D,OAAA;YAAK6D,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3B5D,OAAA;cAAK6D,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD5D,OAAA;gBAAK6D,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC5D,OAAA;kBAAK6D,SAAS,EAAC,sEAAsE;kBAAAD,QAAA,eACnF5D,OAAA,CAACZ,KAAK;oBAACyE,SAAS,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNjE,OAAA;kBAAK6D,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBACnB5D,OAAA;oBAAK6D,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1C5D,OAAA;sBAAI6D,SAAS,EAAC,qCAAqC;sBAAAD,QAAA,EAAEZ,KAAK,CAACZ;oBAAI;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACpEjB,KAAK,CAAC0B,UAAU,MAAKvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgD,EAAE,kBAC5BnD,OAAA;sBAAM6D,SAAS,EAAC,mGAAmG;sBAAAD,QAAA,gBACjH5D,OAAA,CAACT,KAAK;wBAACsE,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,SAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNjE,OAAA;oBAAG6D,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,GAAC,MAAI,EAACZ,KAAK,CAACG,EAAE;kBAAA;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjE,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAMnB,iBAAiB,CAACJ,KAAK,CAACG,EAAE,CAAE;gBAC3CU,SAAS,EAAC,eAAe;gBACzBH,KAAK,EAAC,cAAc;gBAAAE,QAAA,eAEpB5D,OAAA,CAACV,QAAQ;kBAACuE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENjE,OAAA;cAAK6D,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxB5D,OAAA;gBAAK6D,SAAS,EAAC,2CAA2C;gBAAAD,QAAA,gBACxD5D,OAAA;kBAAM6D,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CjE,OAAA;kBAAM6D,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAEZ,KAAK,CAAC2B,OAAO,CAACH;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eAENjE,OAAA;gBAAK6D,SAAS,EAAC,WAAW;gBAAAD,QAAA,GACvBZ,KAAK,CAAC2B,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAEI,MAAM,iBACpC7E,OAAA;kBAAqB6D,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChD5D,OAAA;oBAAK6D,SAAS,EAAC,mEAAmE;oBAAAD,QAAA,eAChF5D,OAAA;sBAAM6D,SAAS,EAAC,mCAAmC;sBAAAD,QAAA,EAChDiB,MAAM,CAACC,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNjE,OAAA;oBAAM6D,SAAS,EAAC,qCAAqC;oBAAAD,QAAA,EAClDiB,MAAM,CAACC;kBAAK;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,EACNY,MAAM,CAAC1B,EAAE,KAAKH,KAAK,CAAC0B,UAAU,iBAC7B1E,OAAA,CAACT,KAAK;oBAACsE,SAAS,EAAC;kBAA8B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAClD;gBAAA,GAXOY,MAAM,CAAC1B,EAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYd,CACN,CAAC,EACDjB,KAAK,CAAC2B,OAAO,CAACH,MAAM,GAAG,CAAC,iBACvBxE,OAAA;kBAAK6D,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,GAAC,GACpC,EAACZ,KAAK,CAAC2B,OAAO,CAACH,MAAM,GAAG,CAAC,EAAC,eAC7B;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjE,OAAA;cAAK6D,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjD5D,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAACE,KAAK,CAACG,EAAE,CAAE;gBAC3CU,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,EACrC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAnEEjB,KAAK,CAACG,EAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoEb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAzD,eAAe,iBACdR,OAAA;QAAK6D,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F5D,OAAA;UAAK6D,SAAS,EAAC,yCAAyC;UAAAD,QAAA,gBACtD5D,OAAA;YAAI6D,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EjE,OAAA;YAAMiF,QAAQ,EAAElD,iBAAkB;YAAA6B,QAAA,gBAChC5D,OAAA;cAAK6D,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB5D,OAAA;gBAAOkF,OAAO,EAAC,WAAW;gBAACrB,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEpF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjE,OAAA;gBACEmD,EAAE,EAAC,WAAW;gBACde,IAAI,EAAC,MAAM;gBACXE,KAAK,EAAElD,YAAa;gBACpBmD,QAAQ,EAAGrC,CAAC,IAAKb,eAAe,CAACa,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;gBACjDD,WAAW,EAAC,iDAAiD;gBAC7DN,SAAS,EAAC,aAAa;gBACvBsB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjE,OAAA;cAAK6D,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB5D,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbK,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,KAAK,CAAE;gBACzCoD,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACjC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA;gBAAQkE,IAAI,EAAC,QAAQ;gBAACL,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAvD,aAAa,iBACZV,OAAA;QAAK6D,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F5D,OAAA;UAAK6D,SAAS,EAAC,yCAAyC;UAAAD,QAAA,gBACtD5D,OAAA;YAAI6D,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFjE,OAAA;YAAG6D,SAAS,EAAC,4BAA4B;YAAAD,QAAA,EAAC;UAE1C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJjE,OAAA;YAAMiF,QAAQ,EAAExC,eAAgB;YAAAmB,QAAA,gBAC9B5D,OAAA;cAAK6D,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB5D,OAAA;gBAAOkF,OAAO,EAAC,SAAS;gBAACrB,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAElF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjE,OAAA;gBACEmD,EAAE,EAAC,SAAS;gBACZe,IAAI,EAAC,QAAQ;gBACbE,KAAK,EAAEhD,WAAY;gBACnBiD,QAAQ,EAAGrC,CAAC,IAAKX,cAAc,CAACW,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;gBAChDD,WAAW,EAAC,4BAA4B;gBACxCN,SAAS,EAAC,aAAa;gBACvBsB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFjE,OAAA;gBAAG6D,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAE1C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjE,OAAA;cAAK6D,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB5D,OAAA;gBAAOkF,OAAO,EAAC,aAAa;gBAACrB,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEtF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjE,OAAA;gBACEmD,EAAE,EAAC,aAAa;gBAChBiB,KAAK,EAAE9C,WAAY;gBACnB+C,QAAQ,EAAGrC,CAAC,IAAKT,cAAc,CAACS,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;gBAChDD,WAAW,EAAC,gDAAgD;gBAC5DN,SAAS,EAAC,yBAAyB;gBACnCuB,IAAI,EAAE,CAAE;gBACRC,SAAS,EAAE;cAAI;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACFjE,OAAA;gBAAG6D,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,GACtCtC,WAAW,CAACkD,MAAM,EAAC,iBACtB;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjE,OAAA;cAAK6D,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB5D,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbK,OAAO,EAAEA,CAAA,KAAM;kBACb5D,gBAAgB,CAAC,KAAK,CAAC;kBACvBU,cAAc,CAAC,EAAE,CAAC;kBAClBE,cAAc,CAAC,EAAE,CAAC;gBACpB,CAAE;gBACFsC,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACjC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA;gBAAQkE,IAAI,EAAC,QAAQ;gBAACL,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArD,gBAAgB,IAAII,aAAa,iBAChChB,OAAA;QAAK6D,SAAS,EAAC,gFAAgF;QAAAD,QAAA,eAC7F5D,OAAA;UAAK6D,SAAS,EAAC,uEAAuE;UAAAD,QAAA,gBACpF5D,OAAA;YAAK6D,SAAS,EAAC,wCAAwC;YAAAD,QAAA,gBACrD5D,OAAA;cAAI6D,SAAS,EAAC,qCAAqC;cAAAD,QAAA,EAAE5C,aAAa,CAACoB;YAAI;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7EjE,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAM1D,mBAAmB,CAAC,KAAK,CAAE;cAC1CgD,SAAS,EAAC,mCAAmC;cAAAD,QAAA,eAE7C5D,OAAA,CAACP,CAAC;gBAACoE,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjE,OAAA;YAAK6D,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAExB5D,OAAA;cAAK6D,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC5D,OAAA;gBAAI6D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEjE,OAAA;gBAAK6D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,gBAC7C5D,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAM6D,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChDjE,OAAA;oBAAM6D,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,EAAE5C,aAAa,CAACmC;kBAAE;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNjE,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAM6D,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/CjE,OAAA;oBAAM6D,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,EAAE5C,aAAa,CAAC2D,OAAO,CAACH;kBAAM;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjE,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAI6D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DjE,OAAA;gBAAK6D,SAAS,EAAC,WAAW;gBAAAD,QAAA,EACvB5C,aAAa,CAAC2D,OAAO,CAACF,GAAG,CAAEI,MAAM,iBAChC7E,OAAA;kBAAqB6D,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,eAC1F5D,OAAA;oBAAK6D,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,gBAChC5D,OAAA;sBAAK6D,SAAS,EAAC,sEAAsE;sBAAAD,QAAA,eACnF5D,OAAA;wBAAM6D,SAAS,EAAC,sCAAsC;wBAAAD,QAAA,EACnDiB,MAAM,CAACC,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;sBAAC;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNjE,OAAA;sBAAK6D,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBACnB5D,OAAA;wBAAG6D,SAAS,EAAC,mCAAmC;wBAAAD,QAAA,EAAEiB,MAAM,CAACC;sBAAK;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAClEY,MAAM,CAAC1B,EAAE,KAAKnC,aAAa,CAAC0D,UAAU,iBACrC1E,OAAA;wBAAG6D,SAAS,EAAC,2CAA2C;wBAAAD,QAAA,gBACtD5D,OAAA,CAACT,KAAK;0BAACsE,SAAS,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAEpC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAhBEY,MAAM,CAAC1B,EAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBd,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjE,OAAA;cAAK6D,SAAS,EAAC,0DAA0D;cAAAD,QAAA,eACvE5D,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAM1D,mBAAmB,CAAC,KAAK,CAAE;gBAC1CgD,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAC1B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAnD,mBAAmB,IAAIE,aAAa,iBACnChB,OAAA,CAACH,oBAAoB;QACnByF,MAAM,EAAExE,mBAAoB;QAC5ByE,OAAO,EAAEA,CAAA,KAAMxE,sBAAsB,CAAC,KAAK,CAAE;QAC7CgC,OAAO,EAAE/B,aAAa,CAACmC,EAAG;QAC1BqC,SAAS,EAAExE,aAAa,CAACoB,IAAK;QAC9BqD,OAAO,EAAEzE,aAAa,CAAC0D,UAAU,MAAKvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgD,EAAE,CAAC;QAC/CuC,cAAc,EAAErC;MAAmB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC/D,EAAA,CAhaID,MAAgB;EAAA,QACHL,OAAO;AAAA;AAAA+F,EAAA,GADpB1F,MAAgB;AAkatB,eAAeA,MAAM;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}