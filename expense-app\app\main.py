from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from .database import Base, engine
from .routers import auth, groups, expenses, nlp, approvals, group_management, settings
# Create database tables on startup
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="Expense Tracker API",
    version="1.0.0",
    description="""
    ## Expense Tracker API with NLP Integration

    A comprehensive expense tracking system that supports:

    * **User Management**: Registration, authentication with JWT tokens
    * **Group Management**: Create and join expense-sharing groups
    * **Expense Tracking**: Add expenses split among group members
    * **Settlement System**: Settle debts between users with partial/full payments
    * **Natural Language Processing**: Use LLaMA 3.3 70B via Groq Cloud for natural language commands

    ### Natural Language Examples:
    * "Add lunch 25 split among roommates"
    * "Settle 500 with Ahmed"
    * "What's my balance?"
    * "Show me expenses from work group"

    ### Authentication:
    All endpoints (except registration and login) require a Bearer token in the Authorization header.
    """,
    contact={
        "name": "Expense Tracker API",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
    },
)

# Include routers (remove duplicate prefixes since routers already have them)
app.include_router(auth.router)
app.include_router(groups.router)
app.include_router(expenses.router)
app.include_router(nlp.router)
app.include_router(approvals.router)
app.include_router(group_management.router)
app.include_router(settings.router)

def log_routes(app):
    for route in app.router.routes:
        print(f"ROUTE {route.name}: {route.path}")
log_routes(app)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT"
        }
    }
    for path in openapi_schema["paths"].values():
        for method in path.values():
            method.setdefault("security", [{"BearerAuth": []}])
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi