/**
 * Integration service for connecting backend events to frontend notifications
 */

import { notificationService } from './notificationService';

export class NotificationIntegration {
  
  /**
   * Process expense creation response and trigger appropriate notifications
   */
  static handleExpenseCreated(expenseData: any, groupName?: string, userEmail?: string) {
    notificationService.notifyExpenseCreated({
      description: expenseData.description,
      amount: expenseData.total,
      payer: userEmail || 'You',
      groupName
    });
  }

  /**
   * Process settlement creation response and trigger notification
   */
  static handleSettlementSent(settlementResponse: any, recipientEmail: string) {
    notificationService.notifySettlementSent({
      amount: parseFloat(settlementResponse.total_paid),
      recipient: recipientEmail
    });
  }

  /**
   * Process expense approval and trigger notification
   */
  static handleExpenseApproved(expenseData: any, approverEmail: string) {
    notificationService.notifyExpenseApproved({
      description: expenseData.description,
      amount: expenseData.total,
      approver: approverEmail
    });
  }

  /**
   * Process settlement confirmation and trigger notification
   */
  static handleSettlementConfirmed(settlementData: any, confirmerEmail: string) {
    notificationService.notifySettlementConfirmed({
      amount: settlementData.amount,
      confirmer: confirmerEmail
    });
  }

  /**
   * Process join request approval/rejection
   */
  static handleJoinRequestProcessed(groupName: string, approved: boolean) {
    if (approved) {
      notificationService.notifyJoinRequestApproved({ groupName });
    } else {
      notificationService.notifyJoinRequestRejected({ groupName });
    }
  }

  /**
   * Handle incoming settlement that needs confirmation
   */
  static handleSettlementReceived(settlementData: any, payerEmail: string) {
    notificationService.notifySettlementReceived({
      amount: settlementData.amount,
      payer: payerEmail
    });
  }

  /**
   * Handle new expense that requires approval from current user
   */
  static handleExpenseNeedsApproval(expenseData: any, payerEmail: string, groupName?: string) {
    notificationService.notifyExpenseNeedsApproval({
      description: expenseData.description,
      amount: expenseData.total,
      payer: payerEmail,
      groupName
    });
  }

  /**
   * Simulate real-time notifications for demo/testing purposes
   * This can be used to test the notification system
   */
  static simulateRealTimeEvent(eventType: string, data: any) {
    switch (eventType) {
      case 'expense_created':
        this.handleExpenseCreated(data.expense, data.groupName, data.userEmail);
        break;
      case 'settlement_sent':
        this.handleSettlementSent(data.settlement, data.recipientEmail);
        break;
      case 'expense_approved':
        this.handleExpenseApproved(data.expense, data.approverEmail);
        break;
      case 'settlement_confirmed':
        this.handleSettlementConfirmed(data.settlement, data.confirmerEmail);
        break;
      case 'join_request_processed':
        this.handleJoinRequestProcessed(data.groupName, data.approved);
        break;
      case 'settlement_received':
        this.handleSettlementReceived(data.settlement, data.payerEmail);
        break;
      case 'expense_needs_approval':
        this.handleExpenseNeedsApproval(data.expense, data.payerEmail, data.groupName);
        break;
      default:
        console.warn('Unknown notification event type:', eventType);
    }
  }

  /**
   * Initialize real-time notification polling (for future WebSocket integration)
   */
  static initializeRealTimeNotifications() {
    // This could be enhanced with WebSocket connections to the backend
    // For now, we rely on manual triggers from user actions
    console.log('Real-time notification system initialized');
  }

  /**
   * Clean up notification system
   */
  static cleanup() {
    // Clean up any listeners or connections
    console.log('Notification system cleaned up');
  }
}

export default NotificationIntegration;
