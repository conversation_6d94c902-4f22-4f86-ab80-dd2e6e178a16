"""
Enhanced group management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from ..database import get_db
from ..models import User
from ..services.group_management_service import GroupManagementService
from ..routers.auth import get_current_user
from .. import schemas

router = APIRouter(prefix="/group-management", tags=["group-management"])

@router.post("/groups", response_model=schemas.GroupResponse)
def create_group_enhanced(
    group_data: schemas.GroupCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new group with enhanced features
    """
    try:
        group_service = GroupManagementService(db)
        group = group_service.create_group(
            creator=current_user,
            name=group_data.name,
            description=getattr(group_data, 'description', None)
        )
        return group
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/groups/{group_id}/join-request")
def request_to_join_group(
    group_id: int,
    message: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Request to join a group (requires owner approval)
    """
    try:
        group_service = GroupManagementService(db)
        join_request = group_service.request_to_join_group(current_user, group_id, message)
        
        return {
            "request_id": join_request.id,
            "group_id": group_id,
            "status": join_request.status,
            "message": "Join request submitted successfully. Waiting for owner approval."
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/groups/join-requests")
def get_pending_join_requests(
    group_id: int = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get pending join requests for groups owned by the current user
    """
    group_service = GroupManagementService(db)
    requests = group_service.get_pending_join_requests(current_user, group_id)
    
    result = []
    for request in requests:
        result.append({
            "request_id": request.id,
            "group_id": request.group_id,
            "group_name": request.group.name,
            "user_id": request.user_id,
            "user_email": request.user.email,
            "message": request.message,
            "created_at": request.created_at.isoformat()
        })
    
    return result

@router.post("/groups/join-requests/{request_id}/process")
def process_join_request(
    request_id: int,
    approved: bool,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Approve or reject a join request (group owner only)
    """
    try:
        group_service = GroupManagementService(db)
        join_request = group_service.process_join_request(current_user, request_id, approved)
        
        return {
            "request_id": request_id,
            "approved": approved,
            "group_id": join_request.group_id,
            "user_email": join_request.user.email,
            "message": f"Join request {'approved' if approved else 'rejected'} successfully"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/groups/{group_id}/members/{user_id}")
def remove_member(
    group_id: int,
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Remove a member from the group (group owner only)
    """
    try:
        group_service = GroupManagementService(db)
        result = group_service.remove_member(current_user, group_id, user_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/groups/{group_id}/leave")
def leave_group(
    group_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Leave a group (members only, not owner)
    """
    try:
        group_service = GroupManagementService(db)
        result = group_service.leave_group(current_user, group_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/groups/{group_id}/transfer-ownership")
def transfer_ownership(
    group_id: int,
    transfer_data: schemas.TransferOwnershipRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Transfer group ownership to another member (current owner only)
    """
    try:
        group_service = GroupManagementService(db)
        result = group_service.transfer_ownership(current_user, group_id, transfer_data.new_owner_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/groups/{group_id}")
def update_group(
    group_id: int,
    name: str = None,
    description: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update group details (group owner only)
    """
    try:
        group_service = GroupManagementService(db)
        group = group_service.update_group(current_user, group_id, name, description)
        return {
            "group_id": group.id,
            "name": group.name,
            "description": group.description,
            "message": "Group updated successfully"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/groups/{group_id}/details")
def get_group_details(
    group_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed group information including member balances
    """
    try:
        group_service = GroupManagementService(db)
        details = group_service.get_group_details(current_user, group_id)
        return details
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/groups/{group_id}/can-leave")
def check_can_leave_group(
    group_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Check if the current user can leave the group (no unsettled debts)
    """
    try:
        group_service = GroupManagementService(db)
        balance = group_service._get_user_balance_in_group(current_user.id, group_id)
        
        can_leave = abs(balance) <= 0.01  # Allow for small rounding errors
        
        return {
            "can_leave": can_leave,
            "balance": balance,
            "message": "You can leave this group" if can_leave else f"You have an unsettled balance of ${abs(balance):.2f}"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
