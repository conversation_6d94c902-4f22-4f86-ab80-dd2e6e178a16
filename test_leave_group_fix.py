#!/usr/bin/env python3
"""
Test script to verify the leave group balance calculation fix
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'expense-app'))

from app.database import SessionLocal
from app.services.group_management_service import GroupManagementService
from app.services.expense_service import ExpenseService
from app.models import User, Group, Settlement
from sqlalchemy import and_

def test_balance_consistency():
    """Test that dashboard and group management show consistent balances"""
    db = SessionLocal()
    try:
        print("=== Testing Balance Calculation Consistency ===\n")
        
        # Get users with confirmed settlements
        user1 = db.query(User).filter(User.email == '<EMAIL>').first()
        user2 = db.query(User).filter(User.email == '<EMAIL>').first()
        
        if not user1 or not user2:
            print("❌ Test users not found")
            return False
            
        print(f"Testing users: {user1.email} and {user2.email}")
        
        # Get their shared groups
        shared_groups = []
        for group in user1.groups:
            if user2 in group.members:
                shared_groups.append(group)
        
        if not shared_groups:
            print("❌ No shared groups found")
            return False
            
        group = shared_groups[0]
        print(f"Testing in group: {group.name} (ID: {group.id})\n")
        
        # Test group management balance calculation
        group_service = GroupManagementService(db)
        user1_group_balance = group_service._get_user_balance_in_group(user1.id, group.id)
        user2_group_balance = group_service._get_user_balance_in_group(user2.id, group.id)
        
        # Test dashboard balance calculation
        expense_service = ExpenseService(db)
        user1_dashboard_balances = expense_service.get_user_balances(user1)
        user2_dashboard_balances = expense_service.get_user_balances(user2)
        
        # Find balances between these two users
        user1_owes_user2_dashboard = 0.0
        user2_owes_user1_dashboard = 0.0
        
        for balance in user1_dashboard_balances:
            if balance['user_id'] == user2.id:
                if balance['amount'] > 0:
                    user2_owes_user1_dashboard = float(balance['amount'])
                else:
                    user1_owes_user2_dashboard = abs(float(balance['amount']))
                    
        for balance in user2_dashboard_balances:
            if balance['user_id'] == user1.id:
                if balance['amount'] > 0:
                    user1_owes_user2_dashboard = float(balance['amount'])
                else:
                    user2_owes_user1_dashboard = abs(float(balance['amount']))
        
        print("📊 Balance Comparison:")
        print(f"Group Management - {user1.email}: ${user1_group_balance:.2f}")
        print(f"Group Management - {user2.email}: ${user2_group_balance:.2f}")
        print(f"Dashboard - {user1.email} owes {user2.email}: ${user1_owes_user2_dashboard:.2f}")
        print(f"Dashboard - {user2.email} owes {user1.email}: ${user2_owes_user1_dashboard:.2f}")
        
        # Test can-leave functionality
        print("\n🚪 Can-Leave Test:")
        for user in [user1, user2]:
            balance = group_service._get_user_balance_in_group(user.id, group.id)
            can_leave = abs(balance) <= 0.01
            
            print(f"{user.email}:")
            print(f"  Balance: ${balance:.2f}")
            print(f"  Can leave: {'✅ YES' if can_leave else '❌ NO'}")
            if not can_leave:
                print(f"  Reason: Unsettled balance of ${abs(balance):.2f}")
        
        # Check if balances are consistent (both should be zero or very close)
        consistency_check = (
            abs(user1_group_balance) <= 0.01 and 
            abs(user2_group_balance) <= 0.01 and
            abs(user1_owes_user2_dashboard) <= 0.01 and
            abs(user2_owes_user1_dashboard) <= 0.01
        )
        
        print(f"\n🎯 Consistency Check: {'✅ PASSED' if consistency_check else '❌ FAILED'}")
        
        if consistency_check:
            print("✅ Both users should be able to leave the group!")
        else:
            print("❌ Balance calculations are inconsistent!")
            
        return consistency_check
        
    finally:
        db.close()

def test_settlement_records():
    """Check settlement records in the database"""
    db = SessionLocal()
    try:
        print("\n=== Settlement Records Analysis ===\n")
        
        confirmed_settlements = db.query(Settlement).filter(
            Settlement.status == "confirmed"
        ).all()
        
        print(f"Total confirmed settlements: {len(confirmed_settlements)}")
        
        for settlement in confirmed_settlements:
            payer = db.query(User).filter(User.id == settlement.payer_id).first()
            recipient = db.query(User).filter(User.id == settlement.recipient_id).first()
            print(f"💰 {payer.email} → {recipient.email}: ${settlement.amount}")
            
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Testing Leave Group Balance Calculation Fix\n")
    
    test_settlement_records()
    success = test_balance_consistency()
    
    print(f"\n{'🎉 ALL TESTS PASSED!' if success else '💥 TESTS FAILED!'}")
    print("\nThe leave group validation should now work correctly!")
