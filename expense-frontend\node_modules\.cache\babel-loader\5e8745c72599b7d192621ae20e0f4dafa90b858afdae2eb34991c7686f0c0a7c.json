{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\pages\\\\Approvals.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { CheckCircle, XCircle, Clock, Users, DollarSign, Calendar, AlertCircle, Check, X } from 'lucide-react';\nimport { formatCurrency, formatDate } from '../utils/formatters';\nimport { approvalsAPI } from '../services/api';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport { notificationService } from '../services/notificationService';\nimport { useAuth } from '../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Approvals = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [pendingApprovals, setPendingApprovals] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState([]);\n  const [selectedApprovals, setSelectedApprovals] = useState([]);\n  const loadPendingApprovals = async () => {\n    try {\n      const response = await approvalsAPI.getPendingApprovals();\n      setPendingApprovals(response.data);\n    } catch (error) {\n      toast.error('Failed to load pending approvals');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadPendingApprovals();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadPendingApprovals, []);\n  const handleApproval = async (expenseId, approved) => {\n    setProcessing([...processing, expenseId]);\n    try {\n      await approvalsAPI.approveExpense(expenseId, approved);\n\n      // Remove from pending list\n      const approvedExpense = pendingApprovals.find(a => a.expense_id === expenseId);\n      setPendingApprovals(pendingApprovals.filter(a => a.expense_id !== expenseId));\n\n      // Trigger notification for approval\n      if (approved && approvedExpense) {\n        notificationService.notifyExpenseApproved({\n          description: approvedExpense.description,\n          amount: approvedExpense.total,\n          approver: (user === null || user === void 0 ? void 0 : user.email) || 'You'\n        });\n      }\n      toast.success(`Expense ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || `Failed to ${approved ? 'approve' : 'reject'} expense`);\n    } finally {\n      setProcessing(processing.filter(id => id !== expenseId));\n    }\n  };\n  const handleBulkApproval = async approved => {\n    if (selectedApprovals.length === 0) {\n      toast.error('Please select expenses to approve/reject');\n      return;\n    }\n    try {\n      await approvalsAPI.bulkApproveExpenses(selectedApprovals, approved);\n\n      // Remove approved/rejected expenses from pending list\n      const processedExpenses = pendingApprovals.filter(a => selectedApprovals.includes(a.expense_id));\n      setPendingApprovals(pendingApprovals.filter(a => !selectedApprovals.includes(a.expense_id)));\n      setSelectedApprovals([]);\n\n      // Trigger notifications for bulk approvals\n      if (approved && processedExpenses.length > 0) {\n        processedExpenses.forEach(expense => {\n          notificationService.notifyExpenseApproved({\n            description: expense.description,\n            amount: expense.total,\n            approver: (user === null || user === void 0 ? void 0 : user.email) || 'You'\n          });\n        });\n      }\n      toast.success(`${selectedApprovals.length} expenses ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Failed to process bulk approval');\n    }\n  };\n  const toggleSelection = expenseId => {\n    if (selectedApprovals.includes(expenseId)) {\n      setSelectedApprovals(selectedApprovals.filter(id => id !== expenseId));\n    } else {\n      setSelectedApprovals([...selectedApprovals, expenseId]);\n    }\n  };\n  const selectAll = () => {\n    if (selectedApprovals.length === pendingApprovals.length) {\n      setSelectedApprovals([]);\n    } else {\n      setSelectedApprovals(pendingApprovals.map(a => a.expense_id));\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"Expense Approvals\",\n      subtitle: \"Review and approve pending expenses\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"Expense Approvals\",\n    subtitle: \"Review and approve pending expenses\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Pending Approvals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-orange-600\",\n                  children: pendingApprovals.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-orange-100\",\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"w-6 h-6 text-orange-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: formatCurrency(pendingApprovals.reduce((sum, a) => sum + a.your_share, 0))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-blue-100\",\n                children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"w-6 h-6 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-primary-600\",\n                  children: selectedApprovals.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-primary-100\",\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-6 h-6 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), pendingApprovals.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: selectAll,\n                className: \"btn-ghost text-sm\",\n                children: selectedApprovals.length === pendingApprovals.length ? 'Deselect All' : 'Select All'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [selectedApprovals.length, \" of \", pendingApprovals.length, \" selected\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), selectedApprovals.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleBulkApproval(false),\n                className: \"btn-secondary text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(X, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this), \"Reject Selected\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleBulkApproval(true),\n                className: \"btn-primary text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(Check, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), \"Approve Selected\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), pendingApprovals.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"All Caught Up!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"You have no pending expense approvals at the moment.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: pendingApprovals.map(approval => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card hover:shadow-lg transition-shadow\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: selectedApprovals.includes(approval.expense_id),\n                  onChange: () => toggleSelection(approval.expense_id),\n                  className: \"mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900\",\n                      children: approval.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full\",\n                      children: \"Pending\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 264,\n                        columnNumber: 29\n                      }, this), \"Total: \", formatCurrency(approval.total)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 263,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 29\n                      }, this), approval.group_name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 29\n                      }, this), formatDate(approval.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 29\n                      }, this), \"Paid by: \", approval.payer_email]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 p-3 bg-blue-50 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-blue-800\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [\"Your share: \", formatCurrency(approval.your_share)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 283,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2 ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleApproval(approval.expense_id, false),\n                  disabled: processing.includes(approval.expense_id),\n                  className: \"btn-secondary text-sm disabled:opacity-50\",\n                  children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                    className: \"w-4 h-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this), \"Reject\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleApproval(approval.expense_id, true),\n                  disabled: processing.includes(approval.expense_id),\n                  className: \"btn-primary text-sm disabled:opacity-50\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this), \"Approve\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 17\n          }, this)\n        }, approval.expense_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(Approvals, \"IUfZ4QXnYTCv0aYM8R8MymIIudM=\", false, function () {\n  return [useAuth, useAutoRefresh];\n});\n_c = Approvals;\nexport default Approvals;\nvar _c;\n$RefreshReg$(_c, \"Approvals\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "CheckCircle", "XCircle", "Clock", "Users", "DollarSign", "Calendar", "AlertCircle", "Check", "X", "formatCurrency", "formatDate", "approvalsAPI", "useAutoRefresh", "notificationService", "useAuth", "toast", "jsxDEV", "_jsxDEV", "Approvals", "_s", "user", "pendingApprovals", "setPendingApprovals", "loading", "setLoading", "processing", "setProcessing", "selectedA<PERSON><PERSON><PERSON>", "setSelectedApprovals", "loadPendingApprovals", "response", "getPendingApprovals", "data", "error", "handleApproval", "expenseId", "approved", "approveExpense", "approvedExpense", "find", "a", "expense_id", "filter", "notifyExpenseApproved", "description", "amount", "total", "approver", "email", "success", "_error$response", "_error$response$data", "detail", "id", "handleBulkApproval", "length", "bulkApproveExpenses", "processedExpenses", "includes", "for<PERSON>ach", "expense", "_error$response2", "_error$response2$data", "toggleSelection", "selectAll", "map", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "reduce", "sum", "your_share", "onClick", "approval", "type", "checked", "onChange", "group_name", "created_at", "payer_email", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/pages/Approvals.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { \n  CheckCircle, \n  XCircle, \n  Clock,\n  Users,\n  DollarSign,\n  Calendar,\n  AlertCircle,\n  Check,\n  X\n} from 'lucide-react';\nimport { formatCurrency, formatDate } from '../utils/formatters';\nimport { approvalsAPI } from '../services/api';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport { notificationService } from '../services/notificationService';\nimport { useAuth } from '../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\ninterface PendingApproval {\n  approval_id: number;\n  expense_id: number;\n  description: string;\n  total: number;\n  payer_email: string;\n  group_name: string;\n  created_at: string;\n  your_share: number;\n}\n\nconst Approvals: React.FC = () => {\n  const { user } = useAuth();\n  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState<number[]>([]);\n  const [selectedApprovals, setSelectedApprovals] = useState<number[]>([]);\n\n  const loadPendingApprovals = async () => {\n    try {\n      const response = await approvalsAPI.getPendingApprovals();\n      setPendingApprovals(response.data);\n    } catch (error) {\n      toast.error('Failed to load pending approvals');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadPendingApprovals();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadPendingApprovals, []);\n\n  const handleApproval = async (expenseId: number, approved: boolean) => {\n    setProcessing([...processing, expenseId]);\n    \n    try {\n      await approvalsAPI.approveExpense(expenseId, approved);\n      \n      // Remove from pending list\n      const approvedExpense = pendingApprovals.find(a => a.expense_id === expenseId);\n      setPendingApprovals(pendingApprovals.filter(a => a.expense_id !== expenseId));\n\n      // Trigger notification for approval\n      if (approved && approvedExpense) {\n        notificationService.notifyExpenseApproved({\n          description: approvedExpense.description,\n          amount: approvedExpense.total,\n          approver: user?.email || 'You'\n        });\n      }\n\n      toast.success(`Expense ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || `Failed to ${approved ? 'approve' : 'reject'} expense`);\n    } finally {\n      setProcessing(processing.filter(id => id !== expenseId));\n    }\n  };\n\n  const handleBulkApproval = async (approved: boolean) => {\n    if (selectedApprovals.length === 0) {\n      toast.error('Please select expenses to approve/reject');\n      return;\n    }\n\n    try {\n      await approvalsAPI.bulkApproveExpenses(selectedApprovals, approved);\n      \n      // Remove approved/rejected expenses from pending list\n      const processedExpenses = pendingApprovals.filter(a => selectedApprovals.includes(a.expense_id));\n      setPendingApprovals(pendingApprovals.filter(a => !selectedApprovals.includes(a.expense_id)));\n      setSelectedApprovals([]);\n\n      // Trigger notifications for bulk approvals\n      if (approved && processedExpenses.length > 0) {\n        processedExpenses.forEach(expense => {\n          notificationService.notifyExpenseApproved({\n            description: expense.description,\n            amount: expense.total,\n            approver: user?.email || 'You'\n          });\n        });\n      }\n\n      toast.success(`${selectedApprovals.length} expenses ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to process bulk approval');\n    }\n  };\n\n  const toggleSelection = (expenseId: number) => {\n    if (selectedApprovals.includes(expenseId)) {\n      setSelectedApprovals(selectedApprovals.filter(id => id !== expenseId));\n    } else {\n      setSelectedApprovals([...selectedApprovals, expenseId]);\n    }\n  };\n\n  const selectAll = () => {\n    if (selectedApprovals.length === pendingApprovals.length) {\n      setSelectedApprovals([]);\n    } else {\n      setSelectedApprovals(pendingApprovals.map(a => a.expense_id));\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout title=\"Expense Approvals\" subtitle=\"Review and approve pending expenses\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout title=\"Expense Approvals\" subtitle=\"Review and approve pending expenses\">\n      <div className=\"space-y-6\">\n        {/* Summary Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending Approvals</p>\n                  <p className=\"text-2xl font-bold text-orange-600\">{pendingApprovals.length}</p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-orange-100\">\n                  <Clock className=\"w-6 h-6 text-orange-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Amount</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {formatCurrency(pendingApprovals.reduce((sum, a) => sum + a.your_share, 0))}\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-blue-100\">\n                  <DollarSign className=\"w-6 h-6 text-blue-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Selected</p>\n                  <p className=\"text-2xl font-bold text-primary-600\">{selectedApprovals.length}</p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-primary-100\">\n                  <CheckCircle className=\"w-6 h-6 text-primary-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bulk Actions */}\n        {pendingApprovals.length > 0 && (\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <button\n                    onClick={selectAll}\n                    className=\"btn-ghost text-sm\"\n                  >\n                    {selectedApprovals.length === pendingApprovals.length ? 'Deselect All' : 'Select All'}\n                  </button>\n                  <span className=\"text-sm text-gray-500\">\n                    {selectedApprovals.length} of {pendingApprovals.length} selected\n                  </span>\n                </div>\n                \n                {selectedApprovals.length > 0 && (\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={() => handleBulkApproval(false)}\n                      className=\"btn-secondary text-sm\"\n                    >\n                      <X className=\"w-4 h-4 mr-1\" />\n                      Reject Selected\n                    </button>\n                    <button\n                      onClick={() => handleBulkApproval(true)}\n                      className=\"btn-primary text-sm\"\n                    >\n                      <Check className=\"w-4 h-4 mr-1\" />\n                      Approve Selected\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Pending Approvals List */}\n        {pendingApprovals.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">All Caught Up!</h3>\n            <p className=\"text-gray-500\">\n              You have no pending expense approvals at the moment.\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {pendingApprovals.map((approval) => (\n              <div key={approval.expense_id} className=\"card hover:shadow-lg transition-shadow\">\n                <div className=\"card-content\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start space-x-4\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedApprovals.includes(approval.expense_id)}\n                        onChange={() => toggleSelection(approval.expense_id)}\n                        className=\"mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                      />\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">{approval.description}</h3>\n                          <span className=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full\">\n                            Pending\n                          </span>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600\">\n                          <div className=\"flex items-center\">\n                            <DollarSign className=\"w-4 h-4 mr-1\" />\n                            Total: {formatCurrency(approval.total)}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Users className=\"w-4 h-4 mr-1\" />\n                            {approval.group_name}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"w-4 h-4 mr-1\" />\n                            {formatDate(approval.created_at)}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <AlertCircle className=\"w-4 h-4 mr-1\" />\n                            Paid by: {approval.payer_email}\n                          </div>\n                        </div>\n                        \n                        <div className=\"mt-3 p-3 bg-blue-50 rounded-lg\">\n                          <p className=\"text-sm text-blue-800\">\n                            <strong>Your share: {formatCurrency(approval.your_share)}</strong>\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex space-x-2 ml-4\">\n                      <button\n                        onClick={() => handleApproval(approval.expense_id, false)}\n                        disabled={processing.includes(approval.expense_id)}\n                        className=\"btn-secondary text-sm disabled:opacity-50\"\n                      >\n                        <XCircle className=\"w-4 h-4 mr-1\" />\n                        Reject\n                      </button>\n                      <button\n                        onClick={() => handleApproval(approval.expense_id, true)}\n                        disabled={processing.includes(approval.expense_id)}\n                        className=\"btn-primary text-sm disabled:opacity-50\"\n                      >\n                        <CheckCircle className=\"w-4 h-4 mr-1\" />\n                        Approve\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Approvals;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SACEC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,KAAK,EACLC,CAAC,QACI,cAAc;AACrB,SAASC,cAAc,EAAEC,UAAU,QAAQ,qBAAqB;AAChE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAapC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAoB,EAAE,CAAC;EAC/E,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAW,EAAE,CAAC;EAC1D,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAW,EAAE,CAAC;EAExE,MAAMgC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnB,YAAY,CAACoB,mBAAmB,CAAC,CAAC;MACzDT,mBAAmB,CAACQ,QAAQ,CAACE,IAAI,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlB,KAAK,CAACkB,KAAK,CAAC,kCAAkC,CAAC;IACjD,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED1B,SAAS,CAAC,MAAM;IACd+B,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjB,cAAc,CAACiB,oBAAoB,EAAE,EAAE,CAAC;EAExC,MAAMK,cAAc,GAAG,MAAAA,CAAOC,SAAiB,EAAEC,QAAiB,KAAK;IACrEV,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAEU,SAAS,CAAC,CAAC;IAEzC,IAAI;MACF,MAAMxB,YAAY,CAAC0B,cAAc,CAACF,SAAS,EAAEC,QAAQ,CAAC;;MAEtD;MACA,MAAME,eAAe,GAAGjB,gBAAgB,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,KAAKN,SAAS,CAAC;MAC9Eb,mBAAmB,CAACD,gBAAgB,CAACqB,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,UAAU,KAAKN,SAAS,CAAC,CAAC;;MAE7E;MACA,IAAIC,QAAQ,IAAIE,eAAe,EAAE;QAC/BzB,mBAAmB,CAAC8B,qBAAqB,CAAC;UACxCC,WAAW,EAAEN,eAAe,CAACM,WAAW;UACxCC,MAAM,EAAEP,eAAe,CAACQ,KAAK;UAC7BC,QAAQ,EAAE,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,KAAK,KAAI;QAC3B,CAAC,CAAC;MACJ;MAEAjC,KAAK,CAACkC,OAAO,CAAC,WAAWb,QAAQ,GAAG,UAAU,GAAG,UAAU,gBAAgB,CAAC;IAC9E,CAAC,CAAC,OAAOH,KAAU,EAAE;MAAA,IAAAiB,eAAA,EAAAC,oBAAA;MACnBpC,KAAK,CAACkB,KAAK,CAAC,EAAAiB,eAAA,GAAAjB,KAAK,CAACH,QAAQ,cAAAoB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlB,IAAI,cAAAmB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,aAAahB,QAAQ,GAAG,SAAS,GAAG,QAAQ,UAAU,CAAC;IACrG,CAAC,SAAS;MACRV,aAAa,CAACD,UAAU,CAACiB,MAAM,CAACW,EAAE,IAAIA,EAAE,KAAKlB,SAAS,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAG,MAAOlB,QAAiB,IAAK;IACtD,IAAIT,iBAAiB,CAAC4B,MAAM,KAAK,CAAC,EAAE;MAClCxC,KAAK,CAACkB,KAAK,CAAC,0CAA0C,CAAC;MACvD;IACF;IAEA,IAAI;MACF,MAAMtB,YAAY,CAAC6C,mBAAmB,CAAC7B,iBAAiB,EAAES,QAAQ,CAAC;;MAEnE;MACA,MAAMqB,iBAAiB,GAAGpC,gBAAgB,CAACqB,MAAM,CAACF,CAAC,IAAIb,iBAAiB,CAAC+B,QAAQ,CAAClB,CAAC,CAACC,UAAU,CAAC,CAAC;MAChGnB,mBAAmB,CAACD,gBAAgB,CAACqB,MAAM,CAACF,CAAC,IAAI,CAACb,iBAAiB,CAAC+B,QAAQ,CAAClB,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC;MAC5Fb,oBAAoB,CAAC,EAAE,CAAC;;MAExB;MACA,IAAIQ,QAAQ,IAAIqB,iBAAiB,CAACF,MAAM,GAAG,CAAC,EAAE;QAC5CE,iBAAiB,CAACE,OAAO,CAACC,OAAO,IAAI;UACnC/C,mBAAmB,CAAC8B,qBAAqB,CAAC;YACxCC,WAAW,EAAEgB,OAAO,CAAChB,WAAW;YAChCC,MAAM,EAAEe,OAAO,CAACd,KAAK;YACrBC,QAAQ,EAAE,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,KAAK,KAAI;UAC3B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEAjC,KAAK,CAACkC,OAAO,CAAC,GAAGtB,iBAAiB,CAAC4B,MAAM,aAAanB,QAAQ,GAAG,UAAU,GAAG,UAAU,gBAAgB,CAAC;IAC3G,CAAC,CAAC,OAAOH,KAAU,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACnB/C,KAAK,CAACkB,KAAK,CAAC,EAAA4B,gBAAA,GAAA5B,KAAK,CAACH,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBV,MAAM,KAAI,iCAAiC,CAAC;IAChF;EACF,CAAC;EAED,MAAMW,eAAe,GAAI5B,SAAiB,IAAK;IAC7C,IAAIR,iBAAiB,CAAC+B,QAAQ,CAACvB,SAAS,CAAC,EAAE;MACzCP,oBAAoB,CAACD,iBAAiB,CAACe,MAAM,CAACW,EAAE,IAAIA,EAAE,KAAKlB,SAAS,CAAC,CAAC;IACxE,CAAC,MAAM;MACLP,oBAAoB,CAAC,CAAC,GAAGD,iBAAiB,EAAEQ,SAAS,CAAC,CAAC;IACzD;EACF,CAAC;EAED,MAAM6B,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIrC,iBAAiB,CAAC4B,MAAM,KAAKlC,gBAAgB,CAACkC,MAAM,EAAE;MACxD3B,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,MAAM;MACLA,oBAAoB,CAACP,gBAAgB,CAAC4C,GAAG,CAACzB,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC;IAC/D;EACF,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACEN,OAAA,CAAClB,MAAM;MAACmE,KAAK,EAAC,mBAAmB;MAACC,QAAQ,EAAC,qCAAqC;MAAAC,QAAA,eAC9EnD,OAAA;QAAKoD,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpDnD,OAAA;UAAKoD,SAAS,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACExD,OAAA,CAAClB,MAAM;IAACmE,KAAK,EAAC,mBAAmB;IAACC,QAAQ,EAAC,qCAAqC;IAAAC,QAAA,eAC9EnD,OAAA;MAAKoD,SAAS,EAAC,WAAW;MAAAD,QAAA,gBAExBnD,OAAA;QAAKoD,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBACpDnD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBnD,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BnD,OAAA;cAAKoD,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDnD,OAAA;gBAAAmD,QAAA,gBACEnD,OAAA;kBAAGoD,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtExD,OAAA;kBAAGoD,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAE/C,gBAAgB,CAACkC;gBAAM;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACNxD,OAAA;gBAAKoD,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,eAC3CnD,OAAA,CAACf,KAAK;kBAACmE,SAAS,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBnD,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BnD,OAAA;cAAKoD,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDnD,OAAA;gBAAAmD,QAAA,gBACEnD,OAAA;kBAAGoD,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjExD,OAAA;kBAAGoD,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAC5C3D,cAAc,CAACY,gBAAgB,CAACqD,MAAM,CAAC,CAACC,GAAG,EAAEnC,CAAC,KAAKmC,GAAG,GAAGnC,CAAC,CAACoC,UAAU,EAAE,CAAC,CAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNxD,OAAA;gBAAKoD,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,eACzCnD,OAAA,CAACb,UAAU;kBAACiE,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBnD,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BnD,OAAA;cAAKoD,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDnD,OAAA;gBAAAmD,QAAA,gBACEnD,OAAA;kBAAGoD,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7DxD,OAAA;kBAAGoD,SAAS,EAAC,qCAAqC;kBAAAD,QAAA,EAAEzC,iBAAiB,CAAC4B;gBAAM;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNxD,OAAA;gBAAKoD,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,eAC5CnD,OAAA,CAACjB,WAAW;kBAACqE,SAAS,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLpD,gBAAgB,CAACkC,MAAM,GAAG,CAAC,iBAC1BtC,OAAA;QAAKoD,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBnD,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3BnD,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDnD,OAAA;cAAKoD,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1CnD,OAAA;gBACE4D,OAAO,EAAEb,SAAU;gBACnBK,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAE5BzC,iBAAiB,CAAC4B,MAAM,KAAKlC,gBAAgB,CAACkC,MAAM,GAAG,cAAc,GAAG;cAAY;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eACTxD,OAAA;gBAAMoD,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,GACpCzC,iBAAiB,CAAC4B,MAAM,EAAC,MAAI,EAAClC,gBAAgB,CAACkC,MAAM,EAAC,WACzD;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEL9C,iBAAiB,CAAC4B,MAAM,GAAG,CAAC,iBAC3BtC,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7BnD,OAAA;gBACE4D,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAAC,KAAK,CAAE;gBACzCe,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBAEjCnD,OAAA,CAACT,CAAC;kBAAC6D,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEhC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxD,OAAA;gBACE4D,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAAC,IAAI,CAAE;gBACxCe,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,gBAE/BnD,OAAA,CAACV,KAAK;kBAAC8D,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAEpC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGApD,gBAAgB,CAACkC,MAAM,KAAK,CAAC,gBAC5BtC,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChCnD,OAAA,CAACjB,WAAW;UAACqE,SAAS,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjExD,OAAA;UAAIoD,SAAS,EAAC,wCAAwC;UAAAD,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1ExD,OAAA;UAAGoD,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAE7B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENxD,OAAA;QAAKoD,SAAS,EAAC,WAAW;QAAAD,QAAA,EACvB/C,gBAAgB,CAAC4C,GAAG,CAAEa,QAAQ,iBAC7B7D,OAAA;UAA+BoD,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eAC/EnD,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BnD,OAAA;cAAKoD,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC/CnD,OAAA;gBAAKoD,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,gBACzCnD,OAAA;kBACE8D,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAErD,iBAAiB,CAAC+B,QAAQ,CAACoB,QAAQ,CAACrC,UAAU,CAAE;kBACzDwC,QAAQ,EAAEA,CAAA,KAAMlB,eAAe,CAACe,QAAQ,CAACrC,UAAU,CAAE;kBACrD4B,SAAS,EAAC;gBAA8E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eAEFxD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBnD,OAAA;oBAAKoD,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,gBAC/CnD,OAAA;sBAAIoD,SAAS,EAAC,qCAAqC;sBAAAD,QAAA,EAAEU,QAAQ,CAAClC;oBAAW;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/ExD,OAAA;sBAAMoD,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,EAAC;oBAE3F;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENxD,OAAA;oBAAKoD,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,gBAC1EnD,OAAA;sBAAKoD,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCnD,OAAA,CAACb,UAAU;wBAACiE,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAChC,EAAChE,cAAc,CAACqE,QAAQ,CAAChC,KAAK,CAAC;oBAAA;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACNxD,OAAA;sBAAKoD,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCnD,OAAA,CAACd,KAAK;wBAACkE,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACjCK,QAAQ,CAACI,UAAU;oBAAA;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACNxD,OAAA;sBAAKoD,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCnD,OAAA,CAACZ,QAAQ;wBAACgE,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpC/D,UAAU,CAACoE,QAAQ,CAACK,UAAU,CAAC;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC,eACNxD,OAAA;sBAAKoD,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCnD,OAAA,CAACX,WAAW;wBAAC+D,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAC/B,EAACK,QAAQ,CAACM,WAAW;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENxD,OAAA;oBAAKoD,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,eAC7CnD,OAAA;sBAAGoD,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,eAClCnD,OAAA;wBAAAmD,QAAA,GAAQ,cAAY,EAAC3D,cAAc,CAACqE,QAAQ,CAACF,UAAU,CAAC;sBAAA;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxD,OAAA;gBAAKoD,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,gBAClCnD,OAAA;kBACE4D,OAAO,EAAEA,CAAA,KAAM3C,cAAc,CAAC4C,QAAQ,CAACrC,UAAU,EAAE,KAAK,CAAE;kBAC1D4C,QAAQ,EAAE5D,UAAU,CAACiC,QAAQ,CAACoB,QAAQ,CAACrC,UAAU,CAAE;kBACnD4B,SAAS,EAAC,2CAA2C;kBAAAD,QAAA,gBAErDnD,OAAA,CAAChB,OAAO;oBAACoE,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxD,OAAA;kBACE4D,OAAO,EAAEA,CAAA,KAAM3C,cAAc,CAAC4C,QAAQ,CAACrC,UAAU,EAAE,IAAI,CAAE;kBACzD4C,QAAQ,EAAE5D,UAAU,CAACiC,QAAQ,CAACoB,QAAQ,CAACrC,UAAU,CAAE;kBACnD4B,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,gBAEnDnD,OAAA,CAACjB,WAAW;oBAACqE,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjEEK,QAAQ,CAACrC,UAAU;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkExB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACtD,EAAA,CA5RID,SAAmB;EAAA,QACNJ,OAAO,EAsBxBF,cAAc;AAAA;AAAA0E,EAAA,GAvBVpE,SAAmB;AA8RzB,eAAeA,SAAS;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}