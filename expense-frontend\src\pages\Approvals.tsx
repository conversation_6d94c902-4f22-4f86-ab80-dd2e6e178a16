import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import { 
  CheckCircle, 
  XCircle, 
  Clock,
  Users,
  DollarSign,
  Calendar,
  AlertCircle,
  Check,
  X
} from 'lucide-react';
import { formatCurrency, formatDate } from '../utils/formatters';
import { approvalsAPI } from '../services/api';
import { useAutoRefresh } from '../hooks/useAutoRefresh';
import { notificationService } from '../services/notificationService';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

interface PendingApproval {
  approval_id: number;
  expense_id: number;
  description: string;
  total: number;
  payer_email: string;
  group_name: string;
  created_at: string;
  your_share: number;
}

const Approvals: React.FC = () => {
  const { user } = useAuth();
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<number[]>([]);
  const [selectedApprovals, setSelectedApprovals] = useState<number[]>([]);

  const loadPendingApprovals = async () => {
    try {
      const response = await approvalsAPI.getPendingApprovals();
      setPendingApprovals(response.data);
    } catch (error) {
      toast.error('Failed to load pending approvals');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPendingApprovals();
  }, []);

  // Auto-refresh data when notifications indicate changes
  useAutoRefresh(loadPendingApprovals, []);

  const handleApproval = async (expenseId: number, approved: boolean) => {
    setProcessing([...processing, expenseId]);
    
    try {
      await approvalsAPI.approveExpense(expenseId, approved);
      
      // Remove from pending list
      const approvedExpense = pendingApprovals.find(a => a.expense_id === expenseId);
      setPendingApprovals(pendingApprovals.filter(a => a.expense_id !== expenseId));

      // Trigger notification for approval
      if (approved && approvedExpense) {
        notificationService.notifyExpenseApproved({
          description: approvedExpense.description,
          amount: approvedExpense.total,
          approver: user?.email || 'You'
        });
      }

      toast.success(`Expense ${approved ? 'approved' : 'rejected'} successfully!`);
    } catch (error: any) {
      toast.error(error.response?.data?.detail || `Failed to ${approved ? 'approve' : 'reject'} expense`);
    } finally {
      setProcessing(processing.filter(id => id !== expenseId));
    }
  };

  const handleBulkApproval = async (approved: boolean) => {
    if (selectedApprovals.length === 0) {
      toast.error('Please select expenses to approve/reject');
      return;
    }

    try {
      await approvalsAPI.bulkApproveExpenses(selectedApprovals, approved);
      
      // Remove approved/rejected expenses from pending list
      const processedExpenses = pendingApprovals.filter(a => selectedApprovals.includes(a.expense_id));
      setPendingApprovals(pendingApprovals.filter(a => !selectedApprovals.includes(a.expense_id)));
      setSelectedApprovals([]);

      // Trigger notifications for bulk approvals
      if (approved && processedExpenses.length > 0) {
        processedExpenses.forEach(expense => {
          notificationService.notifyExpenseApproved({
            description: expense.description,
            amount: expense.total,
            approver: user?.email || 'You'
          });
        });
      }

      toast.success(`${selectedApprovals.length} expenses ${approved ? 'approved' : 'rejected'} successfully!`);
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to process bulk approval');
    }
  };

  const toggleSelection = (expenseId: number) => {
    if (selectedApprovals.includes(expenseId)) {
      setSelectedApprovals(selectedApprovals.filter(id => id !== expenseId));
    } else {
      setSelectedApprovals([...selectedApprovals, expenseId]);
    }
  };

  const selectAll = () => {
    if (selectedApprovals.length === pendingApprovals.length) {
      setSelectedApprovals([]);
    } else {
      setSelectedApprovals(pendingApprovals.map(a => a.expense_id));
    }
  };

  if (loading) {
    return (
      <Layout title="Expense Approvals" subtitle="Review and approve pending expenses">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Expense Approvals" subtitle="Review and approve pending expenses">
      <div className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
                  <p className="text-2xl font-bold text-orange-600">{pendingApprovals.length}</p>
                </div>
                <div className="p-3 rounded-lg bg-orange-100">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Amount</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(pendingApprovals.reduce((sum, a) => sum + a.your_share, 0))}
                  </p>
                </div>
                <div className="p-3 rounded-lg bg-blue-100">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Selected</p>
                  <p className="text-2xl font-bold text-primary-600">{selectedApprovals.length}</p>
                </div>
                <div className="p-3 rounded-lg bg-primary-100">
                  <CheckCircle className="w-6 h-6 text-primary-600" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bulk Actions */}
        {pendingApprovals.length > 0 && (
          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={selectAll}
                    className="btn-ghost text-sm"
                  >
                    {selectedApprovals.length === pendingApprovals.length ? 'Deselect All' : 'Select All'}
                  </button>
                  <span className="text-sm text-gray-500">
                    {selectedApprovals.length} of {pendingApprovals.length} selected
                  </span>
                </div>
                
                {selectedApprovals.length > 0 && (
                  <div className="flex space-x-3">
                    <button
                      onClick={() => handleBulkApproval(false)}
                      className="btn-secondary text-sm"
                    >
                      <X className="w-4 h-4 mr-1" />
                      Reject Selected
                    </button>
                    <button
                      onClick={() => handleBulkApproval(true)}
                      className="btn-primary text-sm"
                    >
                      <Check className="w-4 h-4 mr-1" />
                      Approve Selected
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Pending Approvals List */}
        {pendingApprovals.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">All Caught Up!</h3>
            <p className="text-gray-500">
              You have no pending expense approvals at the moment.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingApprovals.map((approval) => (
              <div key={approval.expense_id} className="card hover:shadow-lg transition-shadow">
                <div className="card-content">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedApprovals.includes(approval.expense_id)}
                        onChange={() => toggleSelection(approval.expense_id)}
                        className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{approval.description}</h3>
                          <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                            Pending
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <DollarSign className="w-4 h-4 mr-1" />
                            Total: {formatCurrency(approval.total)}
                          </div>
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-1" />
                            {approval.group_name}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {formatDate(approval.created_at)}
                          </div>
                          <div className="flex items-center">
                            <AlertCircle className="w-4 h-4 mr-1" />
                            Paid by: {approval.payer_email}
                          </div>
                        </div>
                        
                        <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm text-blue-800">
                            <strong>Your share: {formatCurrency(approval.your_share)}</strong>
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <button
                        onClick={() => handleApproval(approval.expense_id, false)}
                        disabled={processing.includes(approval.expense_id)}
                        className="btn-secondary text-sm disabled:opacity-50"
                      >
                        <XCircle className="w-4 h-4 mr-1" />
                        Reject
                      </button>
                      <button
                        onClick={() => handleApproval(approval.expense_id, true)}
                        disabled={processing.includes(approval.expense_id)}
                        className="btn-primary text-sm disabled:opacity-50"
                      >
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Approve
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Approvals;
