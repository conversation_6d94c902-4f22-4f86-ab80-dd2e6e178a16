import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import {
  Plus,
  Users,
  UserPlus,
  Settings,

  Crown,
  Search,
  X
} from 'lucide-react';
import { Group } from '../types';
import { groupsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import GroupManagementModal from '../components/GroupManagement/GroupManagementModal';
import toast from 'react-hot-toast';

const Groups: React.FC = () => {
  const { user } = useAuth();
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showManagementModal, setShowManagementModal] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [newGroupName, setNewGroupName] = useState('');
  const [joinGroupId, setJoinGroupId] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    try {
      const response = await groupsAPI.getMyGroups();
      setGroups(response.data);
    } catch (error) {
      toast.error('Failed to load groups');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newGroupName.trim()) return;

    try {
      const response = await groupsAPI.createGroup({ name: newGroupName });
      setGroups([...groups, response.data]);
      setNewGroupName('');
      setShowCreateModal(false);
      toast.success('Group created successfully!');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to create group');
    }
  };

  const handleJoinGroup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!joinGroupId.trim()) return;

    try {
      const response = await groupsAPI.joinGroup({ group_id: parseInt(joinGroupId) });
      setGroups([...groups, response.data]);
      setJoinGroupId('');
      setShowJoinModal(false);
      toast.success('Joined group successfully!');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to join group');
    }
  };

  const handleViewDetails = (groupId: number) => {
    const group = groups.find(g => g.id === groupId);
    if (group) {
      setSelectedGroup(group);
      setShowDetailsModal(true);
    }
  };

  const handleManageGroup = (groupId: number) => {
    const group = groups.find(g => g.id === groupId);
    if (group) {
      setSelectedGroup(group);
      setShowManagementModal(true);
    }
  };

  const handleGroupUpdated = () => {
    loadGroups(); // Refresh the groups list
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <Layout title="Groups" subtitle="Manage your expense sharing groups">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Groups" subtitle="Manage your expense sharing groups">
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64"
            />
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={() => setShowJoinModal(true)}
              className="btn-secondary"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Join Group
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Group
            </button>
          </div>
        </div>

        {/* Groups Grid */}
        {filteredGroups.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? 'No groups found' : 'No groups yet'}
            </h3>
            <p className="text-gray-500 mb-6">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : 'Create your first group or join an existing one to start sharing expenses'
              }
            </p>
            {!searchTerm && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="btn-primary"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Group
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGroups.map((group) => (
              <div key={group.id} className="card hover:shadow-lg transition-shadow">
                <div className="card-content">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                        <Users className="w-6 h-6 text-primary-600" />
                      </div>
                      <div className="ml-3">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-semibold text-gray-900">{group.name}</h3>
                          {group.creator_id === user?.id && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              <Crown className="w-3 h-3 mr-1" />
                              Owner
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">ID: {group.id}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => handleManageGroup(group.id)}
                      className="btn-ghost p-2"
                      title="Manage Group"
                    >
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Members</span>
                      <span className="font-medium">{group.members.length}</span>
                    </div>
                    
                    <div className="space-y-2">
                      {group.members.slice(0, 3).map((member) => (
                        <div key={member.id} className="flex items-center">
                          <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium text-gray-600">
                              {member.email.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="ml-2 text-sm text-gray-700 truncate">
                            {member.email}
                          </span>
                          {member.id === group.creator_id && (
                            <Crown className="w-3 h-3 text-yellow-500 ml-1" />
                          )}
                        </div>
                      ))}
                      {group.members.length > 3 && (
                        <div className="text-xs text-gray-500">
                          +{group.members.length - 3} more members
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => handleViewDetails(group.id)}
                      className="w-full btn-ghost text-sm"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Create Group Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Group</h3>
              <form onSubmit={handleCreateGroup}>
                <div className="mb-4">
                  <label htmlFor="groupName" className="block text-sm font-medium text-gray-700 mb-2">
                    Group Name
                  </label>
                  <input
                    id="groupName"
                    type="text"
                    value={newGroupName}
                    onChange={(e) => setNewGroupName(e.target.value)}
                    placeholder="Enter group name (e.g., Office Team, Roommates)"
                    className="input-field"
                    required
                  />
                </div>
                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="flex-1 btn-secondary"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="flex-1 btn-primary">
                    Create Group
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Join Group Modal */}
        {showJoinModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Join Existing Group</h3>
              <form onSubmit={handleJoinGroup}>
                <div className="mb-4">
                  <label htmlFor="groupId" className="block text-sm font-medium text-gray-700 mb-2">
                    Group ID
                  </label>
                  <input
                    id="groupId"
                    type="number"
                    value={joinGroupId}
                    onChange={(e) => setJoinGroupId(e.target.value)}
                    placeholder="Enter the group ID to join"
                    className="input-field"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Ask a group member for the group ID to join
                  </p>
                </div>
                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={() => setShowJoinModal(false)}
                    className="flex-1 btn-secondary"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="flex-1 btn-primary">
                    Join Group
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Group Details Modal */}
        {showDetailsModal && selectedGroup && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">{selectedGroup.name}</h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Group Info */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Group Information</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Group ID:</span>
                      <span className="ml-2 font-medium">{selectedGroup.id}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Members:</span>
                      <span className="ml-2 font-medium">{selectedGroup.members.length}</span>
                    </div>
                  </div>
                </div>

                {/* Members List */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Members</h4>
                  <div className="space-y-2">
                    {selectedGroup.members.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-primary-600">
                              {member.email.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">{member.email}</p>
                            {member.id === selectedGroup.creator_id && (
                              <p className="text-xs text-yellow-600 flex items-center">
                                <Crown className="w-3 h-3 mr-1" />
                                Group Owner
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => setShowDetailsModal(false)}
                    className="btn-secondary"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Group Management Modal */}
        {showManagementModal && selectedGroup && (
          <GroupManagementModal
            isOpen={showManagementModal}
            onClose={() => setShowManagementModal(false)}
            groupId={selectedGroup.id}
            groupName={selectedGroup.name}
            isOwner={selectedGroup.creator_id === user?.id}
            onGroupUpdated={handleGroupUpdated}
          />
        )}
      </div>
    </Layout>
  );
};

export default Groups;
