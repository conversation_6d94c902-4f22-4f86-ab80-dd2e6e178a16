{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\components\\\\UI\\\\ConfirmationDialog.tsx\";\n/**\n * Reusable Confirmation Dialog Component\n * Replaces native browser confirm() to avoid ESLint errors\n */\n\nimport React from 'react';\nimport { AlertTriangle, X } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmationDialog = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  type = 'warning',\n  loading = false\n}) => {\n  if (!isOpen) return null;\n  const getTypeStyles = () => {\n    switch (type) {\n      case 'danger':\n        return {\n          bgColor: 'bg-red-50',\n          borderColor: 'border-red-200',\n          iconColor: 'text-red-600',\n          titleColor: 'text-red-900',\n          messageColor: 'text-red-700',\n          confirmButton: 'bg-red-600 hover:bg-red-700 text-white'\n        };\n      case 'warning':\n        return {\n          bgColor: 'bg-yellow-50',\n          borderColor: 'border-yellow-200',\n          iconColor: 'text-yellow-600',\n          titleColor: 'text-yellow-900',\n          messageColor: 'text-yellow-700',\n          confirmButton: 'bg-yellow-600 hover:bg-yellow-700 text-white'\n        };\n      case 'info':\n        return {\n          bgColor: 'bg-blue-50',\n          borderColor: 'border-blue-200',\n          iconColor: 'text-blue-600',\n          titleColor: 'text-blue-900',\n          messageColor: 'text-blue-700',\n          confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white'\n        };\n      default:\n        return {\n          bgColor: 'bg-gray-50',\n          borderColor: 'border-gray-200',\n          iconColor: 'text-gray-600',\n          titleColor: 'text-gray-900',\n          messageColor: 'text-gray-700',\n          confirmButton: 'bg-gray-600 hover:bg-gray-700 text-white'\n        };\n    }\n  };\n  const styles = getTypeStyles();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg max-w-md w-full shadow-xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-600\",\n          disabled: loading,\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${styles.bgColor} ${styles.borderColor} border rounded-lg p-4`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: `w-6 h-6 ${styles.iconColor} mt-0.5 flex-shrink-0`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: `font-medium ${styles.titleColor} mb-2`,\n                children: \"Confirmation Required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-sm ${styles.messageColor} leading-relaxed`,\n                children: message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          disabled: loading,\n          className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: cancelText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onConfirm,\n          disabled: loading,\n          className: `px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed ${styles.confirmButton} ${loading ? 'cursor-not-allowed' : ''}`,\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Processing...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this) : confirmText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_c = ConfirmationDialog;\nexport default ConfirmationDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfirmationDialog\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "X", "jsxDEV", "_jsxDEV", "ConfirmationDialog", "isOpen", "onClose", "onConfirm", "title", "message", "confirmText", "cancelText", "type", "loading", "getTypeStyles", "bgColor", "borderColor", "iconColor", "titleColor", "messageColor", "confirmButton", "styles", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/components/UI/ConfirmationDialog.tsx"], "sourcesContent": ["/**\n * Reusable Confirmation Dialog Component\n * Replaces native browser confirm() to avoid ESLint errors\n */\n\nimport React from 'react';\nimport { AlertTriangle, X } from 'lucide-react';\n\ninterface ConfirmationDialogProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => void;\n  title: string;\n  message: string;\n  confirmText?: string;\n  cancelText?: string;\n  type?: 'danger' | 'warning' | 'info';\n  loading?: boolean;\n}\n\nconst ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  type = 'warning',\n  loading = false\n}) => {\n  if (!isOpen) return null;\n\n  const getTypeStyles = () => {\n    switch (type) {\n      case 'danger':\n        return {\n          bgColor: 'bg-red-50',\n          borderColor: 'border-red-200',\n          iconColor: 'text-red-600',\n          titleColor: 'text-red-900',\n          messageColor: 'text-red-700',\n          confirmButton: 'bg-red-600 hover:bg-red-700 text-white'\n        };\n      case 'warning':\n        return {\n          bgColor: 'bg-yellow-50',\n          borderColor: 'border-yellow-200',\n          iconColor: 'text-yellow-600',\n          titleColor: 'text-yellow-900',\n          messageColor: 'text-yellow-700',\n          confirmButton: 'bg-yellow-600 hover:bg-yellow-700 text-white'\n        };\n      case 'info':\n        return {\n          bgColor: 'bg-blue-50',\n          borderColor: 'border-blue-200',\n          iconColor: 'text-blue-600',\n          titleColor: 'text-blue-900',\n          messageColor: 'text-blue-700',\n          confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white'\n        };\n      default:\n        return {\n          bgColor: 'bg-gray-50',\n          borderColor: 'border-gray-200',\n          iconColor: 'text-gray-600',\n          titleColor: 'text-gray-900',\n          messageColor: 'text-gray-700',\n          confirmButton: 'bg-gray-600 hover:bg-gray-700 text-white'\n        };\n    }\n  };\n\n  const styles = getTypeStyles();\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-md w-full shadow-xl\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n            disabled={loading}\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          <div className={`${styles.bgColor} ${styles.borderColor} border rounded-lg p-4`}>\n            <div className=\"flex items-start space-x-3\">\n              <AlertTriangle className={`w-6 h-6 ${styles.iconColor} mt-0.5 flex-shrink-0`} />\n              <div className=\"flex-1\">\n                <h4 className={`font-medium ${styles.titleColor} mb-2`}>\n                  Confirmation Required\n                </h4>\n                <p className={`text-sm ${styles.messageColor} leading-relaxed`}>\n                  {message}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50\">\n          <button\n            onClick={onClose}\n            disabled={loading}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {cancelText}\n          </button>\n          <button\n            onClick={onConfirm}\n            disabled={loading}\n            className={`px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed ${styles.confirmButton} ${\n              loading ? 'cursor-not-allowed' : ''\n            }`}\n          >\n            {loading ? (\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                <span>Processing...</span>\n              </div>\n            ) : (\n              confirmText\n            )}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfirmationDialog;\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,CAAC,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAchD,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC,KAAK;EACLC,OAAO;EACPC,WAAW,GAAG,SAAS;EACvBC,UAAU,GAAG,QAAQ;EACrBC,IAAI,GAAG,SAAS;EAChBC,OAAO,GAAG;AACZ,CAAC,KAAK;EACJ,IAAI,CAACR,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQF,IAAI;MACV,KAAK,QAAQ;QACX,OAAO;UACLG,OAAO,EAAE,WAAW;UACpBC,WAAW,EAAE,gBAAgB;UAC7BC,SAAS,EAAE,cAAc;UACzBC,UAAU,EAAE,cAAc;UAC1BC,YAAY,EAAE,cAAc;UAC5BC,aAAa,EAAE;QACjB,CAAC;MACH,KAAK,SAAS;QACZ,OAAO;UACLL,OAAO,EAAE,cAAc;UACvBC,WAAW,EAAE,mBAAmB;UAChCC,SAAS,EAAE,iBAAiB;UAC5BC,UAAU,EAAE,iBAAiB;UAC7BC,YAAY,EAAE,iBAAiB;UAC/BC,aAAa,EAAE;QACjB,CAAC;MACH,KAAK,MAAM;QACT,OAAO;UACLL,OAAO,EAAE,YAAY;UACrBC,WAAW,EAAE,iBAAiB;UAC9BC,SAAS,EAAE,eAAe;UAC1BC,UAAU,EAAE,eAAe;UAC3BC,YAAY,EAAE,eAAe;UAC7BC,aAAa,EAAE;QACjB,CAAC;MACH;QACE,OAAO;UACLL,OAAO,EAAE,YAAY;UACrBC,WAAW,EAAE,iBAAiB;UAC9BC,SAAS,EAAE,eAAe;UAC1BC,UAAU,EAAE,eAAe;UAC3BC,YAAY,EAAE,eAAe;UAC7BC,aAAa,EAAE;QACjB,CAAC;IACL;EACF,CAAC;EAED,MAAMC,MAAM,GAAGP,aAAa,CAAC,CAAC;EAE9B,oBACEX,OAAA;IAAKmB,SAAS,EAAC,gFAAgF;IAAAC,QAAA,eAC7FpB,OAAA;MAAKmB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAE5DpB,OAAA;QAAKmB,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7EpB,OAAA;UAAImB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAEf;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChExB,OAAA;UACEyB,OAAO,EAAEtB,OAAQ;UACjBgB,SAAS,EAAC,mCAAmC;UAC7CO,QAAQ,EAAEhB,OAAQ;UAAAU,QAAA,eAElBpB,OAAA,CAACF,CAAC;YAACqB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxB,OAAA;QAAKmB,SAAS,EAAC,KAAK;QAAAC,QAAA,eAClBpB,OAAA;UAAKmB,SAAS,EAAE,GAAGD,MAAM,CAACN,OAAO,IAAIM,MAAM,CAACL,WAAW,wBAAyB;UAAAO,QAAA,eAC9EpB,OAAA;YAAKmB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCpB,OAAA,CAACH,aAAa;cAACsB,SAAS,EAAE,WAAWD,MAAM,CAACJ,SAAS;YAAwB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChFxB,OAAA;cAAKmB,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBpB,OAAA;gBAAImB,SAAS,EAAE,eAAeD,MAAM,CAACH,UAAU,OAAQ;gBAAAK,QAAA,EAAC;cAExD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxB,OAAA;gBAAGmB,SAAS,EAAE,WAAWD,MAAM,CAACF,YAAY,kBAAmB;gBAAAI,QAAA,EAC5Dd;cAAO;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxB,OAAA;QAAKmB,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAC9FpB,OAAA;UACEyB,OAAO,EAAEtB,OAAQ;UACjBuB,QAAQ,EAAEhB,OAAQ;UAClBS,SAAS,EAAC,oOAAoO;UAAAC,QAAA,EAE7OZ;QAAU;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACTxB,OAAA;UACEyB,OAAO,EAAErB,SAAU;UACnBsB,QAAQ,EAAEhB,OAAQ;UAClBS,SAAS,EAAE,mKAAmKD,MAAM,CAACD,aAAa,IAChMP,OAAO,GAAG,oBAAoB,GAAG,EAAE,EAClC;UAAAU,QAAA,EAEFV,OAAO,gBACNV,OAAA;YAAKmB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpB,OAAA;cAAKmB,SAAS,EAAC;YAA2D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFxB,OAAA;cAAAoB,QAAA,EAAM;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,GAENjB;QACD;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GArHI1B,kBAAqD;AAuH3D,eAAeA,kBAAkB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}