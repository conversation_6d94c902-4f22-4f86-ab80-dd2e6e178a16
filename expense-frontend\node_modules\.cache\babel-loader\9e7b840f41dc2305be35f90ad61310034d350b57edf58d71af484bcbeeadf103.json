{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport LoginForm from './components/Auth/LoginForm';\nimport RegisterForm from './components/Auth/RegisterForm';\nimport Dashboard from './pages/Dashboard';\nimport AIChat from './pages/AIChat';\nimport Groups from './pages/Groups';\nimport Expenses from './pages/Expenses';\nimport Settlements from './pages/Settlements';\nimport Approvals from './pages/Approvals';\nimport SettlementConfirmations from './pages/SettlementConfirmations';\nimport Settings from './pages/Settings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginForm, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(RegisterForm, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/groups\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Groups, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/expenses\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Expenses, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settlements\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Settlements, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/approvals\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Approvals, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settlement-confirmations\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(SettlementConfirmations, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ai-chat\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AIChat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settings\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n          position: \"top-right\",\n          toastOptions: {\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff'\n            },\n            success: {\n              duration: 3000,\n              iconTheme: {\n                primary: '#10B981',\n                secondary: '#fff'\n              }\n            },\n            error: {\n              duration: 4000,\n              iconTheme: {\n                primary: '#EF4444',\n                secondary: '#fff'\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "LoginForm", "RegisterForm", "Dashboard", "AIChat", "Groups", "Expenses", "Settlements", "Approvals", "SettlementConfirmations", "Settings", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport LoginForm from './components/Auth/LoginForm';\nimport RegisterForm from './components/Auth/RegisterForm';\nimport Dashboard from './pages/Dashboard';\nimport AIChat from './pages/AIChat';\nimport Groups from './pages/Groups';\nimport Expenses from './pages/Expenses';\nimport Settlements from './pages/Settlements';\nimport Approvals from './pages/Approvals';\nimport SettlementConfirmations from './pages/SettlementConfirmations';\nimport Settings from './pages/Settings';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            {/* Public routes */}\n            <Route path=\"/login\" element={<LoginForm />} />\n            <Route path=\"/register\" element={<RegisterForm />} />\n            \n            {/* Protected routes */}\n            <Route path=\"/\" element={\n              <ProtectedRoute>\n                <Dashboard />\n              </ProtectedRoute>\n            } />\n            \n            <Route path=\"/groups\" element={\n              <ProtectedRoute>\n                <Groups />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/expenses\" element={\n              <ProtectedRoute>\n                <Expenses />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/settlements\" element={\n              <ProtectedRoute>\n                <Settlements />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/approvals\" element={\n              <ProtectedRoute>\n                <Approvals />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/settlement-confirmations\" element={\n              <ProtectedRoute>\n                <SettlementConfirmations />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/ai-chat\" element={\n              <ProtectedRoute>\n                <AIChat />\n              </ProtectedRoute>\n            } />\n            \n            <Route path=\"/settings\" element={\n              <ProtectedRoute>\n                <Settings />\n              </ProtectedRoute>\n            } />\n            \n            {/* Catch all route */}\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n          </Routes>\n          \n          {/* Toast notifications */}\n          <Toaster\n            position=\"top-right\"\n            toastOptions={{\n              duration: 4000,\n              style: {\n                background: '#363636',\n                color: '#fff',\n              },\n              success: {\n                duration: 3000,\n                iconTheme: {\n                  primary: '#10B981',\n                  secondary: '#fff',\n                },\n              },\n              error: {\n                duration: 4000,\n                iconTheme: {\n                  primary: '#EF4444',\n                  secondary: '#fff',\n                },\n              },\n            }}\n          />\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACb,YAAY;IAAAe,QAAA,eACXF,OAAA,CAAClB,MAAM;MAAAoB,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBF,OAAA,CAACjB,MAAM;UAAAmB,QAAA,gBAELF,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEL,OAAA,CAACX,SAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEL,OAAA,CAACV,YAAY;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGrDT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,GAAG;YAACC,OAAO,eACrBL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACT,SAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,SAAS;YAACC,OAAO,eAC3BL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACP,MAAM;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,WAAW;YAACC,OAAO,eAC7BL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACN,QAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,cAAc;YAACC,OAAO,eAChCL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACL,WAAW;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,YAAY;YAACC,OAAO,eAC9BL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACJ,SAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,2BAA2B;YAACC,OAAO,eAC7CL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACH,uBAAuB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,UAAU;YAACC,OAAO,eAC5BL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACR,MAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,WAAW;YAACC,OAAO,eAC7BL,OAAA,CAACZ,cAAc;cAAAc,QAAA,eACbF,OAAA,CAACF,QAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGJT,OAAA,CAAChB,KAAK;YAACoB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEL,OAAA,CAACf,QAAQ;cAACyB,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eAGTT,OAAA,CAACd,OAAO;UACN0B,QAAQ,EAAC,WAAW;UACpBC,YAAY,EAAE;YACZC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;cACLC,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE;YACT,CAAC;YACDC,OAAO,EAAE;cACPJ,QAAQ,EAAE,IAAI;cACdK,SAAS,EAAE;gBACTC,OAAO,EAAE,SAAS;gBAClBC,SAAS,EAAE;cACb;YACF,CAAC;YACDC,KAAK,EAAE;cACLR,QAAQ,EAAE,IAAI;cACdK,SAAS,EAAE;gBACTC,OAAO,EAAE,SAAS;gBAClBC,SAAS,EAAE;cACb;YACF;UACF;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACc,EAAA,GA5FQtB,GAAG;AA8FZ,eAAeA,GAAG;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}