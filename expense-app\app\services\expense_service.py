from sqlalchemy.orm import Session
from decimal import Decimal
from typing import List
from .. import models, schemas, crud
from ..models import User, Expense, Share, Group, ExpenseStatus
from .approval_service import ApprovalService


class ExpenseService:
    """Service class for expense-related business logic"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_expense(self, current_user: User, expense_data: schemas.ExpenseCreate) -> Expense:
        """Create a new expense and split it among group members with approval system"""
        # Create the expense with pending status
        expense = crud.create_expense_with_shares(self.db, current_user, expense_data)

        # Create approval records for all group members except the payer
        approval_service = ApprovalService(self.db)
        approval_service.create_expense_approvals(expense)

        return expense
    
    def get_user_balances(self, current_user: User) -> List[dict]:
        """Get balance information for the current user (only approved expenses)"""
        return crud.get_user_balances(self.db, current_user, approved_only=True)

    def get_expense_history(self, current_user: User, group_id: int = None) -> List[Expense]:
        """Get expense history for the current user, optionally filtered by group"""
        query = self.db.query(Expense).filter(
            Expense.status == ExpenseStatus.APPROVED.value
        )

        if group_id:
            query = query.filter(Expense.group_id == group_id)

        # Filter to expenses where user is involved (payer or has a share)
        user_expenses = query.join(Share, Expense.id == Share.expense_id).filter(
            (Expense.payer_id == current_user.id) | (Share.user_id == current_user.id)
        ).distinct().all()

        return user_expenses

    def get_pending_approvals(self, current_user: User) -> List[dict]:
        """Get all expenses pending approval by the current user"""
        approval_service = ApprovalService(self.db)
        approvals = approval_service.get_pending_approvals_for_user(current_user.id)

        result = []
        for approval in approvals:
            expense = approval.expense
            result.append({
                "approval_id": approval.id,
                "expense_id": expense.id,
                "description": expense.description,
                "total": float(expense.total),
                "payer_email": expense.payer.email,
                "group_name": expense.group.name,
                "created_at": expense.created_at.isoformat(),
                "your_share": float(expense.total) / (len(expense.shares) + 1)  # +1 for payer
            })

        return result

    def approve_expense(self, current_user: User, expense_id: int, approved: bool) -> dict:
        """Approve or reject an expense"""
        approval_service = ApprovalService(self.db)
        approval = approval_service.approve_expense(expense_id, current_user.id, approved)

        return {
            "expense_id": expense_id,
            "approved": approved,
            "message": f"Expense {'approved' if approved else 'rejected'} successfully"
        }

    def settle_debt(self, current_user: User, settlement_data: schemas.SettlementRequest) -> schemas.SettlementSummary:
        """Create a settlement that requires confirmation from the recipient"""
        from .approval_service import SettlementService

        # Get target user information
        target_user = self.db.query(models.User).filter(models.User.id == settlement_data.target_user_id).first()
        if not target_user:
            raise ValueError("Target user not found")

        settlement_service = SettlementService(self.db)
        settlement = settlement_service.create_settlement(
            payer_id=current_user.id,
            recipient_id=settlement_data.target_user_id,
            amount=settlement_data.amount,
            description=f"Settlement payment"
        )

        # Create a settlement result for the response
        settlement_result = schemas.SettlementResult(
            share_id=0,  # No specific share for pending settlements
            paid_amount=settlement_data.amount,
            remaining_amount=0.0  # Will be calculated after confirmation
        )

        return schemas.SettlementSummary(
            total_paid=settlement_data.amount,
            target_user_email=target_user.email,
            settlements=[settlement_result],
            message=f"Settlement of ${settlement.amount} sent for confirmation. The recipient must confirm receipt before balances are updated."
        )

    def settle_debt_old(self, current_user: User, settlement_data: schemas.SettlementRequest) -> dict:
        """Settle debt between users with comprehensive response"""
        # Get the original shares before settlement to calculate paid amounts
        original_shares = (
            self.db.query(models.Share)
            .join(models.Expense)
            .filter(
                models.Share.user_id == current_user.id,
                models.Share.paid == False,
                models.Expense.payer_id == settlement_data.target_user_id
            )
            .order_by(models.Expense.created_at)
            .all()
        )
        
        if not original_shares:
            raise ValueError("No unpaid debts to that user")
        
        # Store original amounts
        original_amounts = {share.id: share.amount for share in original_shares}
        
        # Get target user info
        target_user = self.db.query(models.User).filter(models.User.id == settlement_data.target_user_id).first()
        if not target_user:
            raise ValueError("Target user not found")
        
        # Perform settlement
        updated_shares = crud.settle_debt(self.db, current_user, settlement_data.target_user_id, settlement_data.amount)
        
        # Convert to response format
        settlements = []
        for share in updated_shares:
            paid_amount = original_amounts[share.id] - share.amount
            settlements.append(schemas.SettlementResult(
                share_id=share.id,
                paid_amount=paid_amount,
                remaining_amount=share.amount
            ))
        
        # Create summary message
        total_paid = settlement_data.amount
        message = f"Successfully paid {total_paid} to {target_user.email}"
        if any(s.remaining_amount > 0 for s in settlements):
            remaining_debt = sum(s.remaining_amount for s in settlements)
            message += f". Remaining debt: {remaining_debt}"
        else:
            message += ". All debts to this user are now settled!"
        
        return schemas.SettlementSummary(
            total_paid=total_paid,
            target_user_email=target_user.email,
            settlements=settlements,
            message=message
        )
    
    def get_expense_history(self, current_user: User, group_id: int = None) -> List[Expense]:
        """Get expense history for user, optionally filtered by group"""
        query = (
            self.db.query(Expense)
            .join(Share)
            .filter(
                (Expense.payer_id == current_user.id) | (Share.user_id == current_user.id)
            )
        )
        
        if group_id:
            query = query.filter(Expense.group_id == group_id)
        
        return query.order_by(Expense.created_at.desc()).all()
