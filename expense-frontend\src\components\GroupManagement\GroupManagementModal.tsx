/**
 * Comprehensive Group Management Modal with owner controls
 */

import React, { useState, useEffect } from 'react';
import { 
  X, 
  Crown, 
  UserMinus, 
  UserCheck, 
  UserX, 
  Settings, 
  LogOut,
  AlertTriangle,
  Edit3,
  Save,
  Users
} from 'lucide-react';
import { groupManagementAPI } from '../../services/api';
import ConfirmationDialog from '../UI/ConfirmationDialog';
import toast from 'react-hot-toast';

interface GroupManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  groupId: number;
  groupName: string;
  isOwner: boolean;
  onGroupUpdated: () => void;
}

interface GroupDetails {
  id: number;
  name: string;
  description: string;
  creator_id: number;
  creator_email: string;
  created_at: string;
  member_count: number;
  members: Array<{
    user_id: number;
    email: string;
    balance: number;
  }>;
  is_owner: boolean;
}

interface JoinRequest {
  request_id: number;
  group_id: number;
  group_name: string;
  user_id: number;
  user_email: string;
  message: string;
  created_at: string;
}

const GroupManagementModal: React.FC<GroupManagementModalProps> = ({
  isOpen,
  onClose,
  groupId,
  groupName,
  isOwner,
  onGroupUpdated
}) => {
  const [activeTab, setActiveTab] = useState<'details' | 'members' | 'requests' | 'settings'>('details');
  const [groupDetails, setGroupDetails] = useState<GroupDetails | null>(null);
  const [joinRequests, setJoinRequests] = useState<JoinRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [processing, setProcessing] = useState<number[]>([]);
  
  // Edit states
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  
  // Leave group state
  const [canLeave, setCanLeave] = useState<{ can_leave: boolean; balance: number; message: string } | null>(null);

  // Transfer ownership state
  const [showTransferOwnership, setShowTransferOwnership] = useState(false);
  const [selectedNewOwner, setSelectedNewOwner] = useState<number | null>(null);
  const [transferConfirmation, setTransferConfirmation] = useState('');

  // Confirmation dialog state
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
    type?: 'danger' | 'warning' | 'info';
    confirmText?: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
  });

  useEffect(() => {
    if (isOpen) {
      loadGroupDetails();
      if (isOwner) {
        loadJoinRequests();
      }
      checkCanLeave();
    }
  }, [isOpen, groupId]);

  const loadGroupDetails = async () => {
    try {
      setLoading(true);
      const response = await groupManagementAPI.getGroupDetails(groupId);
      setGroupDetails(response.data);
      setEditName(response.data.name);
      setEditDescription(response.data.description || '');
    } catch (error: any) {
      console.error('Failed to load group details:', error);
      toast.error(`Failed to load group details: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadJoinRequests = async () => {
    try {
      const response = await groupManagementAPI.getPendingJoinRequests(groupId);
      setJoinRequests(response.data);
    } catch (error) {
      console.error('Failed to load join requests:', error);
    }
  };

  const checkCanLeave = async () => {
    try {
      const response = await groupManagementAPI.checkCanLeave(groupId);
      setCanLeave(response.data);
    } catch (error) {
      console.error('Failed to check leave status:', error);
    }
  };

  const handleUpdateGroup = async () => {
    if (!editName.trim()) {
      toast.error('Group name is required');
      return;
    }

    try {
      await groupManagementAPI.updateGroup(groupId, {
        name: editName,
        description: editDescription
      });
      
      setIsEditing(false);
      toast.success('Group updated successfully');
      loadGroupDetails();
      onGroupUpdated();
    } catch (error) {
      toast.error('Failed to update group');
    }
  };

  const handleRemoveMember = (userId: number, userEmail: string) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Remove Member',
      message: `Are you sure you want to remove ${userEmail} from this group? This action cannot be undone.`,
      type: 'danger',
      confirmText: 'Remove Member',
      onConfirm: () => confirmRemoveMember(userId, userEmail)
    });
  };

  const confirmRemoveMember = async (userId: number, userEmail: string) => {
    setConfirmDialog({ ...confirmDialog, isOpen: false });

    try {
      setProcessing([...processing, userId]);
      await groupManagementAPI.removeMember(groupId, userId);
      toast.success(`${userEmail} removed from group`);
      loadGroupDetails();
      onGroupUpdated();
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to remove member');
    } finally {
      setProcessing(processing.filter(id => id !== userId));
    }
  };

  const handleLeaveGroup = () => {
    if (!canLeave?.can_leave) {
      toast.error(canLeave?.message || 'Cannot leave group');
      return;
    }

    setConfirmDialog({
      isOpen: true,
      title: 'Leave Group',
      message: 'Are you sure you want to leave this group? This action cannot be undone and you will lose access to all group data.',
      type: 'danger',
      confirmText: 'Leave Group',
      onConfirm: confirmLeaveGroup
    });
  };

  const confirmLeaveGroup = async () => {
    setConfirmDialog({ ...confirmDialog, isOpen: false });

    try {
      await groupManagementAPI.leaveGroup(groupId);
      toast.success('You have left the group');
      onClose();
      onGroupUpdated();
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to leave group');
    }
  };

  const handleProcessJoinRequest = async (requestId: number, approved: boolean, userEmail: string) => {
    try {
      setProcessing([...processing, requestId]);
      await groupManagementAPI.processJoinRequest(requestId, approved);
      toast.success(`Join request ${approved ? 'approved' : 'rejected'} for ${userEmail}`);
      loadJoinRequests();
      if (approved) {
        loadGroupDetails(); // Refresh member list
      }
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to process join request');
    } finally {
      setProcessing(processing.filter(id => id !== requestId));
    }
  };

  const handleTransferOwnership = async () => {
    if (!selectedNewOwner) {
      toast.error('Please select a new owner');
      return;
    }

    if (transferConfirmation !== 'TRANSFER') {
      toast.error('Please type TRANSFER to confirm');
      return;
    }

    const newOwner = groupDetails?.members.find(m => m.user_id === selectedNewOwner);
    if (!newOwner) {
      toast.error('Selected user not found');
      return;
    }

    setConfirmDialog({
      isOpen: true,
      title: 'Transfer Ownership',
      message: `Are you sure you want to transfer ownership to ${newOwner.email}? This action cannot be undone and you will lose all owner privileges.`,
      type: 'danger',
      confirmText: 'Transfer Ownership',
      onConfirm: () => confirmTransferOwnership(newOwner.email)
    });
  };

  const confirmTransferOwnership = async (newOwnerEmail: string) => {
    setConfirmDialog({ ...confirmDialog, isOpen: false });

    try {
      setSaving(true);
      await groupManagementAPI.transferOwnership(groupId, selectedNewOwner!);
      toast.success(`Ownership transferred to ${newOwnerEmail}`);

      // Reset transfer state
      setShowTransferOwnership(false);
      setSelectedNewOwner(null);
      setTransferConfirmation('');

      // Refresh group details and close modal since user is no longer owner
      loadGroupDetails();
      onGroupUpdated();

      // Close modal after a short delay to show success message
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to transfer ownership');
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 rounded-lg">
              <Users className="w-6 h-6 text-primary-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{groupName}</h2>
              <p className="text-sm text-gray-600">Group Management</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {['details', 'members', ...(isOwner ? ['requests'] : []), 'settings'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab === 'requests' && joinRequests.length > 0 && (
                  <span className="ml-2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                    {joinRequests.length}
                  </span>
                )}
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <>
              {/* Details Tab */}
              {activeTab === 'details' && groupDetails && (
                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-medium text-gray-900">Group Information</h3>
                      {isOwner && (
                        <button
                          onClick={() => setIsEditing(!isEditing)}
                          className="btn-ghost text-sm"
                        >
                          {isEditing ? <X className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />}
                          {isEditing ? 'Cancel' : 'Edit'}
                        </button>
                      )}
                    </div>
                    
                    {isEditing ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Group Name
                          </label>
                          <input
                            type="text"
                            value={editName}
                            onChange={(e) => setEditName(e.target.value)}
                            className="input-field"
                            placeholder="Enter group name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description
                          </label>
                          <textarea
                            value={editDescription}
                            onChange={(e) => setEditDescription(e.target.value)}
                            className="input-field"
                            rows={3}
                            placeholder="Enter group description (optional)"
                          />
                        </div>
                        <button
                          onClick={handleUpdateGroup}
                          className="btn-primary"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          Save Changes
                        </button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Name:</span>
                          <span className="ml-2 font-medium">{groupDetails.name}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Members:</span>
                          <span className="ml-2 font-medium">{groupDetails.member_count}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Owner:</span>
                          <span className="ml-2 font-medium flex items-center">
                            <Crown className="w-4 h-4 text-yellow-500 mr-1" />
                            {groupDetails.creator_email}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Created:</span>
                          <span className="ml-2 font-medium">
                            {new Date(groupDetails.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        {groupDetails.description && (
                          <div className="col-span-2">
                            <span className="text-gray-600">Description:</span>
                            <p className="mt-1 text-gray-900">{groupDetails.description}</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Members Tab */}
              {activeTab === 'members' && groupDetails && (
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900">Group Members</h3>
                  <div className="space-y-3">
                    {groupDetails.members.map((member) => (
                      <div key={member.user_id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-primary-600">
                              {member.email.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{member.email}</p>
                            <div className="flex items-center space-x-2">
                              {member.user_id === groupDetails.creator_id && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                  <Crown className="w-3 h-3 mr-1" />
                                  Owner
                                </span>
                              )}
                              <span className={`text-sm ${member.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                Balance: ${Math.abs(member.balance).toFixed(2)} {member.balance >= 0 ? 'owed to them' : 'they owe'}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        {isOwner && member.user_id !== groupDetails.creator_id && (
                          <button
                            onClick={() => handleRemoveMember(member.user_id, member.email)}
                            disabled={processing.includes(member.user_id)}
                            className="btn-ghost text-red-600 hover:bg-red-50"
                          >
                            <UserMinus className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Join Requests Tab (Owner Only) */}
              {activeTab === 'requests' && isOwner && (
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900">Pending Join Requests</h3>
                  {joinRequests.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      No pending join requests
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {joinRequests.map((request) => (
                        <div key={request.request_id} className="p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">{request.user_email}</p>
                              <p className="text-sm text-gray-600">
                                Requested {new Date(request.created_at).toLocaleDateString()}
                              </p>
                              {request.message && (
                                <p className="text-sm text-gray-700 mt-1">"{request.message}"</p>
                              )}
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleProcessJoinRequest(request.request_id, true, request.user_email)}
                                disabled={processing.includes(request.request_id)}
                                className="btn-primary text-sm"
                              >
                                <UserCheck className="w-4 h-4 mr-1" />
                                Approve
                              </button>
                              <button
                                onClick={() => handleProcessJoinRequest(request.request_id, false, request.user_email)}
                                disabled={processing.includes(request.request_id)}
                                className="btn-secondary text-sm"
                              >
                                <UserX className="w-4 h-4 mr-1" />
                                Reject
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === 'settings' && (
                <div className="space-y-6">
                  <h3 className="font-medium text-gray-900">Group Settings</h3>
                  
                  {/* Leave Group */}
                  {!isOwner && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                        <div className="flex-1">
                          <h4 className="font-medium text-red-900">Leave Group</h4>
                          <p className="text-sm text-red-700 mt-1">
                            {canLeave?.message || 'Loading...'}
                          </p>
                          {canLeave && (
                            <button
                              onClick={handleLeaveGroup}
                              disabled={!canLeave.can_leave}
                              className={`mt-3 ${
                                canLeave.can_leave 
                                  ? 'btn-danger' 
                                  : 'bg-gray-300 text-gray-500 cursor-not-allowed px-4 py-2 rounded-lg'
                              }`}
                            >
                              <LogOut className="w-4 h-4 mr-2" />
                              Leave Group
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Owner Settings */}
                  {isOwner && (
                    <div className="space-y-4">
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <Crown className="w-5 h-5 text-yellow-600 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-yellow-900">Owner Controls</h4>
                            <p className="text-sm text-yellow-700 mt-1">
                              As the group owner, you can manage members, approve join requests, and transfer ownership.
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Transfer Ownership</h4>
                        <p className="text-sm text-gray-600 mb-3">
                          Transfer ownership to another group member. This action cannot be undone.
                        </p>

                        {!showTransferOwnership ? (
                          <button
                            className="btn-secondary"
                            onClick={() => setShowTransferOwnership(true)}
                          >
                            <Settings className="w-4 h-4 mr-2" />
                            Transfer Ownership
                          </button>
                        ) : (
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Select New Owner
                              </label>
                              <select
                                value={selectedNewOwner || ''}
                                onChange={(e) => setSelectedNewOwner(e.target.value ? parseInt(e.target.value) : null)}
                                className="input-field"
                              >
                                <option value="">Choose a member...</option>
                                {groupDetails?.members
                                  .filter(member => member.user_id !== groupDetails.creator_id)
                                  .map(member => (
                                    <option key={member.user_id} value={member.user_id}>
                                      {member.email}
                                    </option>
                                  ))
                                }
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Type "TRANSFER" to confirm
                              </label>
                              <input
                                type="text"
                                value={transferConfirmation}
                                onChange={(e) => setTransferConfirmation(e.target.value)}
                                className="input-field"
                                placeholder="Type TRANSFER"
                              />
                            </div>

                            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                              <div className="flex items-start space-x-2">
                                <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5" />
                                <div className="text-sm text-red-700">
                                  <p className="font-medium">Warning:</p>
                                  <p>You will lose all owner privileges and cannot undo this action.</p>
                                </div>
                              </div>
                            </div>

                            <div className="flex space-x-3">
                              <button
                                onClick={handleTransferOwnership}
                                disabled={saving || !selectedNewOwner || transferConfirmation !== 'TRANSFER'}
                                className={`${
                                  saving || !selectedNewOwner || transferConfirmation !== 'TRANSFER'
                                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                    : 'bg-red-600 hover:bg-red-700 text-white'
                                } px-4 py-2 rounded-lg font-medium flex items-center`}
                              >
                                <Settings className="w-4 h-4 mr-2" />
                                {saving ? 'Transferring...' : 'Transfer Ownership'}
                              </button>
                              <button
                                onClick={() => {
                                  setShowTransferOwnership(false);
                                  setSelectedNewOwner(null);
                                  setTransferConfirmation('');
                                }}
                                className="btn-secondary"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog({ ...confirmDialog, isOpen: false })}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        type={confirmDialog.type}
        confirmText={confirmDialog.confirmText}
        loading={saving}
      />
    </div>
  );
};

export default GroupManagementModal;
