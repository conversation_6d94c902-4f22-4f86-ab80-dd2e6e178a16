#!/usr/bin/env python3
"""
Test script to verify settlement processing fixes
"""

import requests
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def test_settlement_workflow():
    """Test the complete settlement workflow from creation to confirmation"""
    
    logger.info("🧪 Testing Settlement Workflow Fix")
    logger.info("=" * 50)
    
    # Step 1: Register test users
    logger.info("1. Setting up test users...")
    
    users = [
        {"email": "<EMAIL>", "password": "test123", "groq_api_key": "test_key"},
        {"email": "<EMAIL>", "password": "test123", "groq_api_key": "test_key"}
    ]
    
    tokens = {}
    
    for user in users:
        try:
            # Register
            response = requests.post(f"{BASE_URL}/auth/register", json=user)
            if response.status_code not in [200, 400]:  # 400 if user already exists
                logger.error(f"Failed to register {user['email']}: {response.status_code}")
                return False
            
            # Login
            login_data = {"email": user["email"], "password": user["password"]}
            response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
            if response.status_code != 200:
                logger.error(f"Failed to login {user['email']}: {response.status_code}")
                return False
            
            tokens[user["email"]] = response.json()["access_token"]
            logger.info(f"✅ User {user['email']} ready")
            
        except Exception as e:
            logger.error(f"Error setting up user {user['email']}: {e}")
            return False
    
    # Step 2: Test settlement creation (this was failing with 500 error)
    logger.info("\n2. Testing settlement creation...")
    
    try:
        payer_headers = {"Authorization": f"Bearer {tokens['<EMAIL>']}"}
        
        # Get recipient user ID (we'll need this for settlement)
        response = requests.get(f"{BASE_URL}/auth/me", headers={"Authorization": f"Bearer {tokens['<EMAIL>']}"})
        recipient_id = response.json()["id"]
        
        settlement_data = {
            "target_user_id": recipient_id,
            "amount": 50.00
        }
        
        response = requests.post(f"{BASE_URL}/expenses/settle", json=settlement_data, headers=payer_headers)
        
        logger.info(f"Settlement creation status: {response.status_code}")
        
        if response.status_code == 200:
            settlement_response = response.json()
            logger.info("✅ Settlement created successfully!")
            logger.info(f"Response: {json.dumps(settlement_response, indent=2)}")
            
            # Verify response has required fields
            required_fields = ["total_paid", "target_user_email", "settlements", "message"]
            missing_fields = [field for field in required_fields if field not in settlement_response]
            
            if missing_fields:
                logger.error(f"❌ Missing required fields: {missing_fields}")
                return False
            else:
                logger.info("✅ All required fields present in response")
                
        else:
            logger.error(f"❌ Settlement creation failed: {response.status_code}")
            logger.error(f"Error: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Settlement creation test failed: {e}")
        return False
    
    # Step 3: Test settlement confirmation
    logger.info("\n3. Testing settlement confirmation...")
    
    try:
        recipient_headers = {"Authorization": f"Bearer {tokens['<EMAIL>']}"}
        
        # Get pending settlements for recipient
        response = requests.get(f"{BASE_URL}/approvals/settlements/pending", headers=recipient_headers)
        
        if response.status_code == 200:
            pending_settlements = response.json()
            logger.info(f"✅ Found {len(pending_settlements)} pending settlements")
            
            if pending_settlements:
                settlement_id = pending_settlements[0]["id"]
                
                # Confirm the settlement
                response = requests.post(
                    f"{BASE_URL}/approvals/settlements/{settlement_id}/confirm?confirmed=true",
                    headers=recipient_headers
                )
                
                if response.status_code == 200:
                    logger.info("✅ Settlement confirmed successfully!")
                    logger.info(f"Confirmation response: {json.dumps(response.json(), indent=2)}")
                else:
                    logger.error(f"❌ Settlement confirmation failed: {response.status_code}")
                    logger.error(f"Error: {response.text}")
                    return False
            else:
                logger.warning("⚠️  No pending settlements found to confirm")
        else:
            logger.error(f"❌ Failed to get pending settlements: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Settlement confirmation test failed: {e}")
        return False
    
    # Step 4: Verify balances are updated
    logger.info("\n4. Testing balance updates...")
    
    try:
        # Check payer's balances
        response = requests.get(f"{BASE_URL}/expenses/balances", headers=payer_headers)
        if response.status_code == 200:
            payer_balances = response.json()
            logger.info(f"✅ Payer balances retrieved: {len(payer_balances)} balance entries")
        else:
            logger.error(f"❌ Failed to get payer balances: {response.status_code}")
            return False
        
        # Check recipient's balances
        response = requests.get(f"{BASE_URL}/expenses/balances", headers=recipient_headers)
        if response.status_code == 200:
            recipient_balances = response.json()
            logger.info(f"✅ Recipient balances retrieved: {len(recipient_balances)} balance entries")
        else:
            logger.error(f"❌ Failed to get recipient balances: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Balance verification test failed: {e}")
        return False
    
    logger.info("\n🎉 Settlement workflow test completed successfully!")
    return True

if __name__ == "__main__":
    print("🔧 Testing Settlement Processing Fixes")
    print("=" * 60)
    print("This will test:")
    print("  • Settlement creation (fixing 500 error)")
    print("  • Response schema validation")
    print("  • Settlement confirmation workflow")
    print("  • Balance updates after confirmation")
    print()
    
    success = test_settlement_workflow()
    
    if success:
        print("\n🎉 ALL SETTLEMENT FIXES WORKING!")
        print("✅ Settlement creation no longer returns 500 errors")
        print("✅ Response includes all required fields")
        print("✅ Settlement confirmation workflow functional")
        print("✅ Balance updates working correctly")
    else:
        print("\n❌ SETTLEMENT ISSUES STILL EXIST")
        print("Please check the backend server logs for more details")
