#!/usr/bin/env python3
"""
Test and compare the two join systems: Direct Join vs Join Request
"""

import requests
import json
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'expense-app'))

from app.database import SessionLocal
from app.models import User, Group, GroupJoinRequest

def get_auth_token(email: str, password: str = "testpassword123"):
    """Get JWT token for authentication"""
    try:
        response = requests.post("http://localhost:8000/auth/login", data={
            "username": email,
            "password": password
        })
        if response.status_code == 200:
            return response.json()["access_token"]
        else:
            print(f"Failed to authenticate {email}: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error authenticating: {e}")
        return None

def test_direct_join_system():
    """Test the old direct join system"""
    print("🔧 Testing Direct Join System (/groups/join)")
    
    db = SessionLocal()
    try:
        # Get test users
        users = db.query(User).limit(2).all()
        if len(users) < 2:
            print("❌ Need at least 2 users for testing")
            return False
            
        user1, user2 = users[0], users[1]
        
        # Find a group that user2 is not a member of
        groups = db.query(Group).all()
        test_group = None
        
        for group in groups:
            member_ids = [member.id for member in group.members]
            if user2.id not in member_ids:
                test_group = group
                break
                
        if not test_group:
            print("❌ No suitable group found for testing")
            return False
            
        print(f"👤 Test User: {user2.email}")
        print(f"🏢 Test Group: {test_group.name} (ID: {test_group.id})")
        
        # Get auth token
        token = get_auth_token(user2.email)
        if not token:
            print("❌ Could not authenticate user")
            return False
            
        # Test direct join
        headers = {"Authorization": f"Bearer {token}"}
        data = {"group_id": test_group.id}
        
        try:
            response = requests.post(
                "http://localhost:8000/groups/join",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                print("✅ Direct join successful")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ Direct join failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return False
            
    finally:
        db.close()

def test_join_request_system():
    """Test the new join request system"""
    print("\n🔧 Testing Join Request System (/group-management/groups/{id}/join-request)")
    
    db = SessionLocal()
    try:
        # Get test users
        users = db.query(User).limit(3).all()
        if len(users) < 3:
            print("❌ Need at least 3 users for testing")
            return False
            
        requester = users[0]
        group_owner = users[1]
        
        # Find a group owned by group_owner that requester is not a member of
        owned_groups = db.query(Group).filter(Group.creator_id == group_owner.id).all()
        test_group = None
        
        for group in owned_groups:
            member_ids = [member.id for member in group.members]
            if requester.id not in member_ids:
                test_group = group
                break
                
        if not test_group:
            print("❌ No suitable group found for testing")
            return False
            
        print(f"👤 Requester: {requester.email}")
        print(f"👑 Group Owner: {group_owner.email}")
        print(f"🏢 Test Group: {test_group.name} (ID: {test_group.id})")
        
        # Step 1: Send join request
        requester_token = get_auth_token(requester.email)
        if not requester_token:
            print("❌ Could not authenticate requester")
            return False
            
        headers = {"Authorization": f"Bearer {requester_token}"}
        data = {"message": "Please let me join your group for testing!"}
        
        try:
            response = requests.post(
                f"http://localhost:8000/group-management/groups/{test_group.id}/join-request",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                request_data = response.json()
                print("✅ Join request sent successfully")
                print(f"   Request ID: {request_data['request_id']}")
                request_id = request_data['request_id']
            else:
                print(f"❌ Join request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Join request failed: {e}")
            return False
            
        # Step 2: Check pending requests as group owner
        owner_token = get_auth_token(group_owner.email)
        if not owner_token:
            print("❌ Could not authenticate group owner")
            return False
            
        headers = {"Authorization": f"Bearer {owner_token}"}
        
        try:
            response = requests.get(
                f"http://localhost:8000/group-management/groups/join-requests?group_id={test_group.id}",
                headers=headers
            )
            
            if response.status_code == 200:
                requests_data = response.json()
                print(f"✅ Found {len(requests_data)} pending requests")
                for req in requests_data:
                    print(f"   Request {req['request_id']}: {req['user_email']} -> {req['group_name']}")
            else:
                print(f"❌ Failed to get pending requests: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to get pending requests: {e}")
            return False
            
        # Step 3: Approve the request
        try:
            response = requests.post(
                f"http://localhost:8000/group-management/groups/join-requests/{request_id}/process",
                json={"approved": True},
                headers=headers
            )
            
            if response.status_code == 200:
                approval_data = response.json()
                print("✅ Join request approved successfully")
                print(f"   User {approval_data['user_email']} added to group")
                return True
            else:
                print(f"❌ Failed to approve request: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to approve request: {e}")
            return False
            
    finally:
        db.close()

def compare_systems():
    """Compare the two systems"""
    print("\n📊 System Comparison")
    print("=" * 50)
    
    print("🔄 Direct Join System:")
    print("   ✅ Immediate group membership")
    print("   ❌ No approval process")
    print("   ❌ No owner control")
    print("   ❌ Security risk")
    
    print("\n📝 Join Request System:")
    print("   ✅ Owner approval required")
    print("   ✅ Better security")
    print("   ✅ Audit trail")
    print("   ✅ Message support")
    print("   ❌ More complex workflow")
    
    print("\n💡 Recommendation:")
    print("   The frontend should use the Join Request System")
    print("   for better security and user experience.")

if __name__ == "__main__":
    print("🚀 Testing Both Join Systems\n")
    
    # Test both systems
    direct_success = test_direct_join_system()
    request_success = test_join_request_system()
    
    compare_systems()
    
    print(f"\n📈 Test Results:")
    print(f"   Direct Join System: {'✅ Working' if direct_success else '❌ Failed'}")
    print(f"   Join Request System: {'✅ Working' if request_success else '❌ Failed'}")
    
    if direct_success and request_success:
        print("\n🎉 Both systems are functional!")
        print("💡 Consider updating frontend to use Join Request System")
    else:
        print("\n⚠️ Some systems have issues that need attention")
