{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\pages\\\\Approvals.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { CheckCircle, XCircle, Clock, Users, DollarSign, Calendar, AlertCircle, Check, X } from 'lucide-react';\nimport { formatCurrency, formatDate } from '../utils/formatters';\nimport { approvalsAPI } from '../services/api';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Approvals = () => {\n  _s();\n  const [pendingApprovals, setPendingApprovals] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState([]);\n  const [selectedApprovals, setSelectedApprovals] = useState([]);\n  useEffect(() => {\n    loadPendingApprovals();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadPendingApprovals, []);\n  const loadPendingApprovals = async () => {\n    try {\n      const response = await approvalsAPI.getPendingApprovals();\n      setPendingApprovals(response.data);\n    } catch (error) {\n      toast.error('Failed to load pending approvals');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApproval = async (expenseId, approved) => {\n    setProcessing([...processing, expenseId]);\n    try {\n      await approvalsAPI.approveExpense(expenseId, approved);\n\n      // Remove from pending list\n      setPendingApprovals(pendingApprovals.filter(a => a.expense_id !== expenseId));\n      toast.success(`Expense ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || `Failed to ${approved ? 'approve' : 'reject'} expense`);\n    } finally {\n      setProcessing(processing.filter(id => id !== expenseId));\n    }\n  };\n  const handleBulkApproval = async approved => {\n    if (selectedApprovals.length === 0) {\n      toast.error('Please select expenses to approve/reject');\n      return;\n    }\n    try {\n      await approvalsAPI.bulkApproveExpenses(selectedApprovals, approved);\n\n      // Remove approved/rejected expenses from pending list\n      setPendingApprovals(pendingApprovals.filter(a => !selectedApprovals.includes(a.expense_id)));\n      setSelectedApprovals([]);\n      toast.success(`${selectedApprovals.length} expenses ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Failed to process bulk approval');\n    }\n  };\n  const toggleSelection = expenseId => {\n    if (selectedApprovals.includes(expenseId)) {\n      setSelectedApprovals(selectedApprovals.filter(id => id !== expenseId));\n    } else {\n      setSelectedApprovals([...selectedApprovals, expenseId]);\n    }\n  };\n  const selectAll = () => {\n    if (selectedApprovals.length === pendingApprovals.length) {\n      setSelectedApprovals([]);\n    } else {\n      setSelectedApprovals(pendingApprovals.map(a => a.expense_id));\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"Expense Approvals\",\n      subtitle: \"Review and approve pending expenses\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"Expense Approvals\",\n    subtitle: \"Review and approve pending expenses\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Pending Approvals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-orange-600\",\n                  children: pendingApprovals.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-orange-100\",\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"w-6 h-6 text-orange-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: formatCurrency(pendingApprovals.reduce((sum, a) => sum + a.your_share, 0))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-blue-100\",\n                children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"w-6 h-6 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-primary-600\",\n                  children: selectedApprovals.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-lg bg-primary-100\",\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-6 h-6 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), pendingApprovals.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: selectAll,\n                className: \"btn-ghost text-sm\",\n                children: selectedApprovals.length === pendingApprovals.length ? 'Deselect All' : 'Select All'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [selectedApprovals.length, \" of \", pendingApprovals.length, \" selected\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), selectedApprovals.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleBulkApproval(false),\n                className: \"btn-secondary text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(X, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 23\n                }, this), \"Reject Selected\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleBulkApproval(true),\n                className: \"btn-primary text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(Check, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 23\n                }, this), \"Approve Selected\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), pendingApprovals.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"All Caught Up!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"You have no pending expense approvals at the moment.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: pendingApprovals.map(approval => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card hover:shadow-lg transition-shadow\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: selectedApprovals.includes(approval.expense_id),\n                  onChange: () => toggleSelection(approval.expense_id),\n                  className: \"mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900\",\n                      children: approval.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full\",\n                      children: \"Pending\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 29\n                      }, this), \"Total: \", formatCurrency(approval.total)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 29\n                      }, this), approval.group_name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 29\n                      }, this), formatDate(approval.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 29\n                      }, this), \"Paid by: \", approval.payer_email]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 p-3 bg-blue-50 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-blue-800\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [\"Your share: \", formatCurrency(approval.your_share)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2 ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleApproval(approval.expense_id, false),\n                  disabled: processing.includes(approval.expense_id),\n                  className: \"btn-secondary text-sm disabled:opacity-50\",\n                  children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                    className: \"w-4 h-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 25\n                  }, this), \"Reject\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleApproval(approval.expense_id, true),\n                  disabled: processing.includes(approval.expense_id),\n                  className: \"btn-primary text-sm disabled:opacity-50\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 25\n                  }, this), \"Approve\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this)\n        }, approval.expense_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(Approvals, \"cbb5wiDMqDnRVa4Wljfxs4jiic0=\", false, function () {\n  return [useAutoRefresh];\n});\n_c = Approvals;\nexport default Approvals;\nvar _c;\n$RefreshReg$(_c, \"Approvals\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "CheckCircle", "XCircle", "Clock", "Users", "DollarSign", "Calendar", "AlertCircle", "Check", "X", "formatCurrency", "formatDate", "approvalsAPI", "useAutoRefresh", "toast", "jsxDEV", "_jsxDEV", "Approvals", "_s", "pendingApprovals", "setPendingApprovals", "loading", "setLoading", "processing", "setProcessing", "selectedA<PERSON><PERSON><PERSON>", "setSelectedApprovals", "loadPendingApprovals", "response", "getPendingApprovals", "data", "error", "handleApproval", "expenseId", "approved", "approveExpense", "filter", "a", "expense_id", "success", "_error$response", "_error$response$data", "detail", "id", "handleBulkApproval", "length", "bulkApproveExpenses", "includes", "_error$response2", "_error$response2$data", "toggleSelection", "selectAll", "map", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "reduce", "sum", "your_share", "onClick", "approval", "type", "checked", "onChange", "description", "total", "group_name", "created_at", "payer_email", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/pages/Approvals.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from '../components/Layout/Layout';\nimport { \n  CheckCircle, \n  XCircle, \n  Clock,\n  Users,\n  DollarSign,\n  Calendar,\n  AlertCircle,\n  Check,\n  X\n} from 'lucide-react';\nimport { formatCurrency, formatDate } from '../utils/formatters';\nimport { approvalsAPI } from '../services/api';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport toast from 'react-hot-toast';\n\ninterface PendingApproval {\n  approval_id: number;\n  expense_id: number;\n  description: string;\n  total: number;\n  payer_email: string;\n  group_name: string;\n  created_at: string;\n  your_share: number;\n}\n\nconst Approvals: React.FC = () => {\n  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState<number[]>([]);\n  const [selectedApprovals, setSelectedApprovals] = useState<number[]>([]);\n\n  useEffect(() => {\n    loadPendingApprovals();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadPendingApprovals, []);\n\n  const loadPendingApprovals = async () => {\n    try {\n      const response = await approvalsAPI.getPendingApprovals();\n      setPendingApprovals(response.data);\n    } catch (error) {\n      toast.error('Failed to load pending approvals');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApproval = async (expenseId: number, approved: boolean) => {\n    setProcessing([...processing, expenseId]);\n    \n    try {\n      await approvalsAPI.approveExpense(expenseId, approved);\n      \n      // Remove from pending list\n      setPendingApprovals(pendingApprovals.filter(a => a.expense_id !== expenseId));\n      \n      toast.success(`Expense ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || `Failed to ${approved ? 'approve' : 'reject'} expense`);\n    } finally {\n      setProcessing(processing.filter(id => id !== expenseId));\n    }\n  };\n\n  const handleBulkApproval = async (approved: boolean) => {\n    if (selectedApprovals.length === 0) {\n      toast.error('Please select expenses to approve/reject');\n      return;\n    }\n\n    try {\n      await approvalsAPI.bulkApproveExpenses(selectedApprovals, approved);\n      \n      // Remove approved/rejected expenses from pending list\n      setPendingApprovals(pendingApprovals.filter(a => !selectedApprovals.includes(a.expense_id)));\n      setSelectedApprovals([]);\n      \n      toast.success(`${selectedApprovals.length} expenses ${approved ? 'approved' : 'rejected'} successfully!`);\n    } catch (error: any) {\n      toast.error(error.response?.data?.detail || 'Failed to process bulk approval');\n    }\n  };\n\n  const toggleSelection = (expenseId: number) => {\n    if (selectedApprovals.includes(expenseId)) {\n      setSelectedApprovals(selectedApprovals.filter(id => id !== expenseId));\n    } else {\n      setSelectedApprovals([...selectedApprovals, expenseId]);\n    }\n  };\n\n  const selectAll = () => {\n    if (selectedApprovals.length === pendingApprovals.length) {\n      setSelectedApprovals([]);\n    } else {\n      setSelectedApprovals(pendingApprovals.map(a => a.expense_id));\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout title=\"Expense Approvals\" subtitle=\"Review and approve pending expenses\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout title=\"Expense Approvals\" subtitle=\"Review and approve pending expenses\">\n      <div className=\"space-y-6\">\n        {/* Summary Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending Approvals</p>\n                  <p className=\"text-2xl font-bold text-orange-600\">{pendingApprovals.length}</p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-orange-100\">\n                  <Clock className=\"w-6 h-6 text-orange-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Amount</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {formatCurrency(pendingApprovals.reduce((sum, a) => sum + a.your_share, 0))}\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-blue-100\">\n                  <DollarSign className=\"w-6 h-6 text-blue-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Selected</p>\n                  <p className=\"text-2xl font-bold text-primary-600\">{selectedApprovals.length}</p>\n                </div>\n                <div className=\"p-3 rounded-lg bg-primary-100\">\n                  <CheckCircle className=\"w-6 h-6 text-primary-600\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bulk Actions */}\n        {pendingApprovals.length > 0 && (\n          <div className=\"card\">\n            <div className=\"card-content\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <button\n                    onClick={selectAll}\n                    className=\"btn-ghost text-sm\"\n                  >\n                    {selectedApprovals.length === pendingApprovals.length ? 'Deselect All' : 'Select All'}\n                  </button>\n                  <span className=\"text-sm text-gray-500\">\n                    {selectedApprovals.length} of {pendingApprovals.length} selected\n                  </span>\n                </div>\n                \n                {selectedApprovals.length > 0 && (\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={() => handleBulkApproval(false)}\n                      className=\"btn-secondary text-sm\"\n                    >\n                      <X className=\"w-4 h-4 mr-1\" />\n                      Reject Selected\n                    </button>\n                    <button\n                      onClick={() => handleBulkApproval(true)}\n                      className=\"btn-primary text-sm\"\n                    >\n                      <Check className=\"w-4 h-4 mr-1\" />\n                      Approve Selected\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Pending Approvals List */}\n        {pendingApprovals.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">All Caught Up!</h3>\n            <p className=\"text-gray-500\">\n              You have no pending expense approvals at the moment.\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {pendingApprovals.map((approval) => (\n              <div key={approval.expense_id} className=\"card hover:shadow-lg transition-shadow\">\n                <div className=\"card-content\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start space-x-4\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedApprovals.includes(approval.expense_id)}\n                        onChange={() => toggleSelection(approval.expense_id)}\n                        className=\"mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                      />\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">{approval.description}</h3>\n                          <span className=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full\">\n                            Pending\n                          </span>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600\">\n                          <div className=\"flex items-center\">\n                            <DollarSign className=\"w-4 h-4 mr-1\" />\n                            Total: {formatCurrency(approval.total)}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Users className=\"w-4 h-4 mr-1\" />\n                            {approval.group_name}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"w-4 h-4 mr-1\" />\n                            {formatDate(approval.created_at)}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <AlertCircle className=\"w-4 h-4 mr-1\" />\n                            Paid by: {approval.payer_email}\n                          </div>\n                        </div>\n                        \n                        <div className=\"mt-3 p-3 bg-blue-50 rounded-lg\">\n                          <p className=\"text-sm text-blue-800\">\n                            <strong>Your share: {formatCurrency(approval.your_share)}</strong>\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex space-x-2 ml-4\">\n                      <button\n                        onClick={() => handleApproval(approval.expense_id, false)}\n                        disabled={processing.includes(approval.expense_id)}\n                        className=\"btn-secondary text-sm disabled:opacity-50\"\n                      >\n                        <XCircle className=\"w-4 h-4 mr-1\" />\n                        Reject\n                      </button>\n                      <button\n                        onClick={() => handleApproval(approval.expense_id, true)}\n                        disabled={processing.includes(approval.expense_id)}\n                        className=\"btn-primary text-sm disabled:opacity-50\"\n                      >\n                        <CheckCircle className=\"w-4 h-4 mr-1\" />\n                        Approve\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n};\n\nexport default Approvals;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SACEC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,KAAK,EACLC,CAAC,QACI,cAAc;AACrB,SAASC,cAAc,EAAEC,UAAU,QAAQ,qBAAqB;AAChE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAapC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAoB,EAAE,CAAC;EAC/E,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAW,EAAE,CAAC;EAC1D,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAW,EAAE,CAAC;EAExEC,SAAS,CAAC,MAAM;IACd4B,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAd,cAAc,CAACc,oBAAoB,EAAE,EAAE,CAAC;EAExC,MAAMA,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhB,YAAY,CAACiB,mBAAmB,CAAC,CAAC;MACzDT,mBAAmB,CAACQ,QAAQ,CAACE,IAAI,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,kCAAkC,CAAC;IACjD,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,cAAc,GAAG,MAAAA,CAAOC,SAAiB,EAAEC,QAAiB,KAAK;IACrEV,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAEU,SAAS,CAAC,CAAC;IAEzC,IAAI;MACF,MAAMrB,YAAY,CAACuB,cAAc,CAACF,SAAS,EAAEC,QAAQ,CAAC;;MAEtD;MACAd,mBAAmB,CAACD,gBAAgB,CAACiB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,KAAKL,SAAS,CAAC,CAAC;MAE7EnB,KAAK,CAACyB,OAAO,CAAC,WAAWL,QAAQ,GAAG,UAAU,GAAG,UAAU,gBAAgB,CAAC;IAC9E,CAAC,CAAC,OAAOH,KAAU,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACnB3B,KAAK,CAACiB,KAAK,CAAC,EAAAS,eAAA,GAAAT,KAAK,CAACH,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,aAAaR,QAAQ,GAAG,SAAS,GAAG,QAAQ,UAAU,CAAC;IACrG,CAAC,SAAS;MACRV,aAAa,CAACD,UAAU,CAACa,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAKV,SAAS,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAG,MAAOV,QAAiB,IAAK;IACtD,IAAIT,iBAAiB,CAACoB,MAAM,KAAK,CAAC,EAAE;MAClC/B,KAAK,CAACiB,KAAK,CAAC,0CAA0C,CAAC;MACvD;IACF;IAEA,IAAI;MACF,MAAMnB,YAAY,CAACkC,mBAAmB,CAACrB,iBAAiB,EAAES,QAAQ,CAAC;;MAEnE;MACAd,mBAAmB,CAACD,gBAAgB,CAACiB,MAAM,CAACC,CAAC,IAAI,CAACZ,iBAAiB,CAACsB,QAAQ,CAACV,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC;MAC5FZ,oBAAoB,CAAC,EAAE,CAAC;MAExBZ,KAAK,CAACyB,OAAO,CAAC,GAAGd,iBAAiB,CAACoB,MAAM,aAAaX,QAAQ,GAAG,UAAU,GAAG,UAAU,gBAAgB,CAAC;IAC3G,CAAC,CAAC,OAAOH,KAAU,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACnBnC,KAAK,CAACiB,KAAK,CAAC,EAAAiB,gBAAA,GAAAjB,KAAK,CAACH,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAI,iCAAiC,CAAC;IAChF;EACF,CAAC;EAED,MAAMQ,eAAe,GAAIjB,SAAiB,IAAK;IAC7C,IAAIR,iBAAiB,CAACsB,QAAQ,CAACd,SAAS,CAAC,EAAE;MACzCP,oBAAoB,CAACD,iBAAiB,CAACW,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAKV,SAAS,CAAC,CAAC;IACxE,CAAC,MAAM;MACLP,oBAAoB,CAAC,CAAC,GAAGD,iBAAiB,EAAEQ,SAAS,CAAC,CAAC;IACzD;EACF,CAAC;EAED,MAAMkB,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI1B,iBAAiB,CAACoB,MAAM,KAAK1B,gBAAgB,CAAC0B,MAAM,EAAE;MACxDnB,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,MAAM;MACLA,oBAAoB,CAACP,gBAAgB,CAACiC,GAAG,CAACf,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC;IAC/D;EACF,CAAC;EAED,IAAIjB,OAAO,EAAE;IACX,oBACEL,OAAA,CAAChB,MAAM;MAACqD,KAAK,EAAC,mBAAmB;MAACC,QAAQ,EAAC,qCAAqC;MAAAC,QAAA,eAC9EvC,OAAA;QAAKwC,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpDvC,OAAA;UAAKwC,SAAS,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACE5C,OAAA,CAAChB,MAAM;IAACqD,KAAK,EAAC,mBAAmB;IAACC,QAAQ,EAAC,qCAAqC;IAAAC,QAAA,eAC9EvC,OAAA;MAAKwC,SAAS,EAAC,WAAW;MAAAD,QAAA,gBAExBvC,OAAA;QAAKwC,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBACpDvC,OAAA;UAAKwC,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBvC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BvC,OAAA;cAAKwC,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDvC,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBAAGwC,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtE5C,OAAA;kBAAGwC,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAEpC,gBAAgB,CAAC0B;gBAAM;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACN5C,OAAA;gBAAKwC,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,eAC3CvC,OAAA,CAACb,KAAK;kBAACqD,SAAS,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5C,OAAA;UAAKwC,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBvC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BvC,OAAA;cAAKwC,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDvC,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBAAGwC,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjE5C,OAAA;kBAAGwC,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAC5C7C,cAAc,CAACS,gBAAgB,CAAC0C,MAAM,CAAC,CAACC,GAAG,EAAEzB,CAAC,KAAKyB,GAAG,GAAGzB,CAAC,CAAC0B,UAAU,EAAE,CAAC,CAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN5C,OAAA;gBAAKwC,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,eACzCvC,OAAA,CAACX,UAAU;kBAACmD,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5C,OAAA;UAAKwC,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBvC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BvC,OAAA;cAAKwC,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDvC,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBAAGwC,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7D5C,OAAA;kBAAGwC,SAAS,EAAC,qCAAqC;kBAAAD,QAAA,EAAE9B,iBAAiB,CAACoB;gBAAM;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACN5C,OAAA;gBAAKwC,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,eAC5CvC,OAAA,CAACf,WAAW;kBAACuD,SAAS,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzC,gBAAgB,CAAC0B,MAAM,GAAG,CAAC,iBAC1B7B,OAAA;QAAKwC,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBvC,OAAA;UAAKwC,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3BvC,OAAA;YAAKwC,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDvC,OAAA;cAAKwC,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1CvC,OAAA;gBACEgD,OAAO,EAAEb,SAAU;gBACnBK,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAE5B9B,iBAAiB,CAACoB,MAAM,KAAK1B,gBAAgB,CAAC0B,MAAM,GAAG,cAAc,GAAG;cAAY;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eACT5C,OAAA;gBAAMwC,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,GACpC9B,iBAAiB,CAACoB,MAAM,EAAC,MAAI,EAAC1B,gBAAgB,CAAC0B,MAAM,EAAC,WACzD;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELnC,iBAAiB,CAACoB,MAAM,GAAG,CAAC,iBAC3B7B,OAAA;cAAKwC,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7BvC,OAAA;gBACEgD,OAAO,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,KAAK,CAAE;gBACzCY,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBAEjCvC,OAAA,CAACP,CAAC;kBAAC+C,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEhC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5C,OAAA;gBACEgD,OAAO,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,IAAI,CAAE;gBACxCY,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,gBAE/BvC,OAAA,CAACR,KAAK;kBAACgD,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAEpC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAzC,gBAAgB,CAAC0B,MAAM,KAAK,CAAC,gBAC5B7B,OAAA;QAAKwC,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChCvC,OAAA,CAACf,WAAW;UAACuD,SAAS,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjE5C,OAAA;UAAIwC,SAAS,EAAC,wCAAwC;UAAAD,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E5C,OAAA;UAAGwC,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAE7B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAEN5C,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAD,QAAA,EACvBpC,gBAAgB,CAACiC,GAAG,CAAEa,QAAQ,iBAC7BjD,OAAA;UAA+BwC,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eAC/EvC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BvC,OAAA;cAAKwC,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC/CvC,OAAA;gBAAKwC,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,gBACzCvC,OAAA;kBACEkD,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAE1C,iBAAiB,CAACsB,QAAQ,CAACkB,QAAQ,CAAC3B,UAAU,CAAE;kBACzD8B,QAAQ,EAAEA,CAAA,KAAMlB,eAAe,CAACe,QAAQ,CAAC3B,UAAU,CAAE;kBACrDkB,SAAS,EAAC;gBAA8E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eAEF5C,OAAA;kBAAKwC,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBvC,OAAA;oBAAKwC,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,gBAC/CvC,OAAA;sBAAIwC,SAAS,EAAC,qCAAqC;sBAAAD,QAAA,EAAEU,QAAQ,CAACI;oBAAW;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/E5C,OAAA;sBAAMwC,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,EAAC;oBAE3F;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAEN5C,OAAA;oBAAKwC,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,gBAC1EvC,OAAA;sBAAKwC,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCvC,OAAA,CAACX,UAAU;wBAACmD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAChC,EAAClD,cAAc,CAACuD,QAAQ,CAACK,KAAK,CAAC;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACN5C,OAAA;sBAAKwC,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCvC,OAAA,CAACZ,KAAK;wBAACoD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACjCK,QAAQ,CAACM,UAAU;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACN5C,OAAA;sBAAKwC,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCvC,OAAA,CAACV,QAAQ;wBAACkD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpCjD,UAAU,CAACsD,QAAQ,CAACO,UAAU,CAAC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC,eACN5C,OAAA;sBAAKwC,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChCvC,OAAA,CAACT,WAAW;wBAACiD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAC/B,EAACK,QAAQ,CAACQ,WAAW;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5C,OAAA;oBAAKwC,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,eAC7CvC,OAAA;sBAAGwC,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,eAClCvC,OAAA;wBAAAuC,QAAA,GAAQ,cAAY,EAAC7C,cAAc,CAACuD,QAAQ,CAACF,UAAU,CAAC;sBAAA;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKwC,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,gBAClCvC,OAAA;kBACEgD,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAACiC,QAAQ,CAAC3B,UAAU,EAAE,KAAK,CAAE;kBAC1DoC,QAAQ,EAAEnD,UAAU,CAACwB,QAAQ,CAACkB,QAAQ,CAAC3B,UAAU,CAAE;kBACnDkB,SAAS,EAAC,2CAA2C;kBAAAD,QAAA,gBAErDvC,OAAA,CAACd,OAAO;oBAACsD,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5C,OAAA;kBACEgD,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAACiC,QAAQ,CAAC3B,UAAU,EAAE,IAAI,CAAE;kBACzDoC,QAAQ,EAAEnD,UAAU,CAACwB,QAAQ,CAACkB,QAAQ,CAAC3B,UAAU,CAAE;kBACnDkB,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,gBAEnDvC,OAAA,CAACf,WAAW;oBAACuD,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjEEK,QAAQ,CAAC3B,UAAU;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkExB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC1C,EAAA,CArQID,SAAmB;EAAA,QAWvBJ,cAAc;AAAA;AAAA8D,EAAA,GAXV1D,SAAmB;AAuQzB,eAAeA,SAAS;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}