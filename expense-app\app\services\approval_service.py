"""
Service for handling expense approvals and settlement confirmations
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional
from datetime import datetime

from ..models import User, Expense, ExpenseApproval, Settlement, Group, ExpenseStatus, SettlementStatus
from .. import schemas


class ApprovalService:
    def __init__(self, db: Session):
        self.db = db

    def create_expense_approvals(self, expense: Expense) -> List[ExpenseApproval]:
        """
        Create approval records for all group members except the expense creator
        """
        # Get all group members except the payer
        group_members = self.db.query(User).join(
            Group.members
        ).filter(
            Group.id == expense.group_id,
            User.id != expense.payer_id
        ).all()

        approvals = []
        for member in group_members:
            approval = ExpenseApproval(
                expense_id=expense.id,
                user_id=member.id,
                approved=None  # Pending approval
            )
            self.db.add(approval)
            approvals.append(approval)

        self.db.commit()
        return approvals

    def approve_expense(self, expense_id: int, user_id: int, approved: bool) -> ExpenseApproval:
        """
        Approve or reject an expense by a specific user
        """
        # Find the approval record
        approval = self.db.query(ExpenseApproval).filter(
            and_(
                ExpenseApproval.expense_id == expense_id,
                ExpenseApproval.user_id == user_id
            )
        ).first()

        if not approval:
            raise ValueError("Approval record not found")

        if approval.approved is not None:
            raise ValueError("Expense already approved/rejected by this user")

        # Update approval
        approval.approved = approved
        approval.approved_at = datetime.utcnow()
        self.db.commit()

        # Check if all approvals are complete
        self._check_expense_approval_status(expense_id)

        return approval

    def _check_expense_approval_status(self, expense_id: int):
        """
        Check if all required approvals are complete and update expense status
        """
        expense = self.db.query(Expense).filter(Expense.id == expense_id).first()
        if not expense:
            return

        # Get all approvals for this expense
        approvals = self.db.query(ExpenseApproval).filter(
            ExpenseApproval.expense_id == expense_id
        ).all()

        # Check if any approval is rejected
        if any(approval.approved is False for approval in approvals):
            expense.status = ExpenseStatus.REJECTED.value
            self.db.commit()
            return

        # Check if all approvals are approved
        if all(approval.approved is True for approval in approvals):
            expense.status = ExpenseStatus.APPROVED.value
            expense.approved_at = datetime.utcnow()
            self.db.commit()
            return

        # Otherwise, still pending

    def get_pending_approvals_for_user(self, user_id: int) -> List[ExpenseApproval]:
        """
        Get all pending expense approvals for a user
        """
        return self.db.query(ExpenseApproval).join(Expense).filter(
            and_(
                ExpenseApproval.user_id == user_id,
                ExpenseApproval.approved.is_(None),
                Expense.status == ExpenseStatus.PENDING.value
            )
        ).all()

    def get_expense_approvals(self, expense_id: int) -> List[ExpenseApproval]:
        """
        Get all approvals for a specific expense
        """
        return self.db.query(ExpenseApproval).filter(
            ExpenseApproval.expense_id == expense_id
        ).all()

    def bulk_approve_expenses(self, user_id: int, expense_ids: List[int], approved: bool) -> List[ExpenseApproval]:
        """
        Bulk approve/reject multiple expenses by a user
        """
        approvals = []
        for expense_id in expense_ids:
            try:
                approval = self.approve_expense(expense_id, user_id, approved)
                approvals.append(approval)
            except ValueError:
                # Skip if already approved or not found
                continue
        
        return approvals


class SettlementService:
    def __init__(self, db: Session):
        self.db = db

    def create_settlement(self, payer_id: int, recipient_id: int, amount: float, description: str = None) -> Settlement:
        """
        Create a new settlement that requires confirmation
        """
        settlement = Settlement(
            payer_id=payer_id,
            recipient_id=recipient_id,
            amount=amount,
            description=description,
            status=SettlementStatus.PENDING.value
        )
        
        self.db.add(settlement)
        self.db.commit()
        self.db.refresh(settlement)
        
        return settlement

    def confirm_settlement(self, settlement_id: int, recipient_id: int, confirmed: bool) -> Settlement:
        """
        Confirm or dispute a settlement by the recipient
        """
        settlement = self.db.query(Settlement).filter(Settlement.id == settlement_id).first()
        
        if not settlement:
            raise ValueError("Settlement not found")
        
        if settlement.recipient_id != recipient_id:
            raise ValueError("Only the recipient can confirm this settlement")
        
        if settlement.status != SettlementStatus.PENDING.value:
            raise ValueError("Settlement is not pending confirmation")
        
        if confirmed:
            settlement.status = SettlementStatus.CONFIRMED.value
            settlement.confirmed_at = datetime.utcnow()

            # Apply the settlement to actual balances
            self._apply_settlement_to_balances(settlement)
        else:
            settlement.status = SettlementStatus.DISPUTED.value

        self.db.commit()
        return settlement

    def _apply_settlement_to_balances(self, settlement: Settlement):
        """
        Apply a confirmed settlement to actual user balances by updating expense shares
        """
        from .. import crud
        from ..models import User
        from decimal import Decimal

        # Get the payer (current_user in CRUD function)
        payer = self.db.query(User).filter(User.id == settlement.payer_id).first()
        if not payer:
            raise ValueError("Payer not found")

        # Use the existing settlement logic from CRUD
        # This will mark shares as paid and update balances
        try:
            crud.settle_debt(
                db=self.db,
                current_user=payer,
                target_user_id=settlement.recipient_id,
                payment=Decimal(str(settlement.amount))
            )
        except Exception as e:
            # If settlement application fails, revert the settlement status
            settlement.status = SettlementStatus.DISPUTED.value
            settlement.confirmed_at = None
            raise ValueError(f"Failed to apply settlement to balances: {str(e)}")

    def get_pending_settlements_for_user(self, user_id: int) -> List[Settlement]:
        """
        Get all pending settlements that require confirmation from a user
        """
        return self.db.query(Settlement).filter(
            and_(
                Settlement.recipient_id == user_id,
                Settlement.status == SettlementStatus.PENDING.value
            )
        ).all()

    def get_user_settlements(self, user_id: int) -> List[Settlement]:
        """
        Get all settlements involving a user (sent or received)
        """
        return self.db.query(Settlement).filter(
            or_(
                Settlement.payer_id == user_id,
                Settlement.recipient_id == user_id
            )
        ).order_by(Settlement.created_at.desc()).all()

    def get_settlement_history(self, user_id: int, status: str = None) -> List[Settlement]:
        """
        Get settlement history for a user, optionally filtered by status
        """
        query = self.db.query(Settlement).filter(
            or_(
                Settlement.payer_id == user_id,
                Settlement.recipient_id == user_id
            )
        )
        
        if status:
            query = query.filter(Settlement.status == status)
        
        return query.order_by(Settlement.created_at.desc()).all()
