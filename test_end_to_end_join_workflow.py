#!/usr/bin/env python3
"""
End-to-end test of the complete join request workflow
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'expense-app'))

from app.database import SessionLocal
from app.services.group_management_service import GroupManagementService
from app.models import User, Group, GroupJoinRequest, JoinRequestStatus
from sqlalchemy import and_

def create_test_scenario():
    """Create a clean test scenario"""
    db = SessionLocal()
    try:
        print("🔧 Setting Up Test Scenario\n")
        
        # Get test users
        users = db.query(User).limit(3).all()
        if len(users) < 3:
            print("❌ Need at least 3 users for testing")
            return None, None, None
            
        group_owner = users[0]
        requester1 = users[1] 
        requester2 = users[2]
        
        # Find a group owned by the first user
        test_group = db.query(Group).filter(Group.creator_id == group_owner.id).first()
        if not test_group:
            print("❌ No groups found for testing")
            return None, None, None
            
        # Clean up - remove requesters from group if they're members
        current_members = test_group.members.copy()
        test_group.members = [member for member in current_members 
                             if member.id not in [requester1.id, requester2.id]]
        
        # Clean up any existing join requests
        existing_requests = db.query(GroupJoinRequest).filter(
            and_(
                GroupJoinRequest.group_id == test_group.id,
                GroupJoinRequest.user_id.in_([requester1.id, requester2.id])
            )
        ).all()
        
        for req in existing_requests:
            db.delete(req)
            
        db.commit()
        
        print(f"👑 Group Owner: {group_owner.email}")
        print(f"👤 Requester 1: {requester1.email}")
        print(f"👤 Requester 2: {requester2.email}")
        print(f"🏢 Test Group: {test_group.name} (ID: {test_group.id})")
        print(f"👥 Current Members: {len(test_group.members)}")
        
        return group_owner, [requester1, requester2], test_group
        
    finally:
        db.close()

def test_complete_workflow():
    """Test the complete join request workflow"""
    print("\n🚀 Testing Complete Join Request Workflow\n")
    
    # Setup
    group_owner, requesters, test_group = create_test_scenario()
    if not all([group_owner, requesters, test_group]):
        return False
        
    requester1, requester2 = requesters
    
    db = SessionLocal()
    try:
        group_service = GroupManagementService(db)
        
        print("=== Step 1: Users Send Join Requests ===")
        
        # Requester 1 sends join request (simulating frontend Groups page)
        try:
            request1 = group_service.request_to_join_group(
                requester1, 
                test_group.id, 
                "Hi! I'd like to join your group to share expenses with the team."
            )
            print(f"✅ {requester1.email} sent join request (ID: {request1.id})")
        except ValueError as e:
            print(f"❌ Request 1 failed: {e}")
            return False
            
        # Requester 2 sends join request
        try:
            request2 = group_service.request_to_join_group(
                requester2, 
                test_group.id, 
                "Please add me to the group - I'm part of the team too!"
            )
            print(f"✅ {requester2.email} sent join request (ID: {request2.id})")
        except ValueError as e:
            print(f"❌ Request 2 failed: {e}")
            return False
            
        print("\n=== Step 2: Group Owner Views Pending Requests ===")
        
        # Group owner checks pending requests (simulating Group Management Modal)
        pending_requests = group_service.get_pending_join_requests(group_owner, test_group.id)
        print(f"✅ Group owner sees {len(pending_requests)} pending requests:")
        
        for request in pending_requests:
            user = db.query(User).filter(User.id == request.user_id).first()
            print(f"   📝 Request {request.id}: {user.email}")
            print(f"      Message: \"{request.message}\"")
            print(f"      Submitted: {request.created_at}")
            
        if len(pending_requests) != 2:
            print(f"❌ Expected 2 requests, found {len(pending_requests)}")
            return False
            
        print("\n=== Step 3: Group Owner Processes Requests ===")
        
        # Approve first request
        try:
            approved_request = group_service.process_join_request(
                group_owner, request1.id, True
            )
            print(f"✅ Approved request from {requester1.email}")
            print(f"   Status: {approved_request.status}")
            print(f"   Processed at: {approved_request.processed_at}")
        except ValueError as e:
            print(f"❌ Failed to approve request 1: {e}")
            return False
            
        # Reject second request
        try:
            rejected_request = group_service.process_join_request(
                group_owner, request2.id, False
            )
            print(f"✅ Rejected request from {requester2.email}")
            print(f"   Status: {rejected_request.status}")
        except ValueError as e:
            print(f"❌ Failed to reject request 2: {e}")
            return False
            
        print("\n=== Step 4: Verify Final State ===")
        
        # Check group membership
        db.refresh(test_group)
        current_member_ids = [member.id for member in test_group.members]
        
        print(f"👥 Final group membership ({len(test_group.members)} members):")
        for member in test_group.members:
            role = "👑 Owner" if member.id == test_group.creator_id else "👤 Member"
            print(f"   {role}: {member.email}")
            
        # Verify requester1 was added
        if requester1.id in current_member_ids:
            print(f"✅ {requester1.email} successfully added to group")
        else:
            print(f"❌ {requester1.email} was not added to group")
            return False
            
        # Verify requester2 was NOT added
        if requester2.id not in current_member_ids:
            print(f"✅ {requester2.email} correctly not added (request rejected)")
        else:
            print(f"❌ {requester2.email} was incorrectly added to group")
            return False
            
        # Check no pending requests remain
        remaining_requests = group_service.get_pending_join_requests(group_owner, test_group.id)
        if len(remaining_requests) == 0:
            print(f"✅ No pending requests remaining")
        else:
            print(f"❌ {len(remaining_requests)} requests still pending")
            return False
            
        print("\n=== Step 5: Verify Request History ===")
        
        # Check all requests for this group
        all_requests = db.query(GroupJoinRequest).filter(
            GroupJoinRequest.group_id == test_group.id
        ).all()
        
        print(f"📊 Request history for {test_group.name}:")
        for request in all_requests:
            user = db.query(User).filter(User.id == request.user_id).first()
            status_emoji = {
                'pending': '⏳',
                'approved': '✅', 
                'rejected': '❌'
            }.get(request.status, '❓')
            
            print(f"   {status_emoji} {user.email}: {request.status}")
            if request.processed_at:
                print(f"      Processed: {request.processed_at}")
                
        return True
        
    finally:
        db.close()

def test_authorization_controls():
    """Test that authorization controls work correctly"""
    print("\n🔒 Testing Authorization Controls\n")
    
    group_owner, requesters, test_group = create_test_scenario()
    if not all([group_owner, requesters, test_group]):
        return False
        
    requester1, requester2 = requesters
    
    db = SessionLocal()
    try:
        group_service = GroupManagementService(db)
        
        # Create a test request
        request = group_service.request_to_join_group(
            requester1, test_group.id, "Test request for authorization"
        )
        
        print(f"Created test request: {request.id}")
        
        # Test 1: Non-owner cannot process requests
        try:
            unauthorized_process = group_service.process_join_request(
                requester2, request.id, True
            )
            print("❌ Non-owner should not be able to process requests!")
            return False
        except ValueError as e:
            print(f"✅ Non-owner correctly denied: {e}")
            
        # Test 2: Only group owner can see pending requests
        owner_requests = group_service.get_pending_join_requests(group_owner, test_group.id)
        non_owner_requests = group_service.get_pending_join_requests(requester1, test_group.id)
        
        print(f"✅ Owner sees {len(owner_requests)} requests")
        print(f"✅ Non-owner sees {len(non_owner_requests)} requests")
        
        if len(owner_requests) > 0 and len(non_owner_requests) == 0:
            print("✅ Authorization working correctly")
        else:
            print("❌ Authorization issue detected")
            return False
            
        return True
        
    finally:
        db.close()

if __name__ == "__main__":
    print("🎯 End-to-End Join Request Workflow Test\n")
    
    workflow_test = test_complete_workflow()
    auth_test = test_authorization_controls()
    
    print(f"\n📊 Final Results:")
    print(f"   Complete Workflow: {'✅ PASSED' if workflow_test else '❌ FAILED'}")
    print(f"   Authorization Controls: {'✅ PASSED' if auth_test else '❌ FAILED'}")
    
    if workflow_test and auth_test:
        print(f"\n🎉 ALL END-TO-END TESTS PASSED!")
        print("\n✅ Join request system is fully functional:")
        print("   • Users can send join requests from Groups page")
        print("   • Requests include custom messages")
        print("   • Group owners see requests in Group Management Modal")
        print("   • Owners can approve/reject requests")
        print("   • Approved users are added to groups")
        print("   • Rejected users remain outside groups")
        print("   • Authorization controls work correctly")
        print("   • No pending requests remain after processing")
        
        print(f"\n🚀 The Groups page now uses the proper join request system!")
    else:
        print(f"\n💥 SOME TESTS FAILED!")
        print("⚠️ Please check the issues above")
