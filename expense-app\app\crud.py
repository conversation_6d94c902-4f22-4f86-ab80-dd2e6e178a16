from sqlalchemy.orm import Session
from . import models, schemas, security
from decimal import Decimal
from collections import defaultdict

def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    hashed_pw = security.hash_password(user.password)
    db_user = models.User(
        email=user.email,
        hashed_password=hashed_pw,
        groq_api_key=user.groq_api_key
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

from .security import verify_password

def authenticate_user(db: Session, email: str, password: str):
    user = db.query(models.User).filter(models.User.email == email).first()
    if not user or not verify_password(password, user.hashed_password):
        return None
    return user

from sqlalchemy.orm import Session
from . import models, schemas

def create_group(db: Session, group: schemas.GroupCreate, creator_id: int):
    db_group = models.Group(
        name=group.name,
        description=getattr(group, 'description', None),
        creator_id=creator_id
    )
    creator = db.query(models.User).get(creator_id)

    if creator:
        db_group.members.append(creator)

    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    return db_group

def get_user_groups(db: Session, user_id: int):
    user = db.query(models.User).get(user_id)
    return user.groups if user else []

def join_group(db: Session, group_id: int, user_id: int):
    group = db.query(models.Group).filter(models.Group.id == group_id).first()
    user = db.query(models.User).get(user_id)
    if group and user and user not in group.members:
        group.members.append(user)
        db.commit()
    return group

def create_expense_with_shares(
    db: Session,
    current_user: models.User,
    expense_in: schemas.ExpenseCreate
) -> models.Expense:
    group = db.query(models.Group).filter(models.Group.id == expense_in.group_id).first()
    if not group:
        raise ValueError("Group not found")

    if current_user not in group.members:
        raise ValueError("You are not a member of this group")

    members = group.members
    total_members = len(members)
    if total_members <= 1:
        raise ValueError("Not enough members to split expense")

    # Per-person share (everyone equally)
    per_person_share = (expense_in.total / Decimal(total_members)).quantize(Decimal("0.01"))

    # Create the expense
    expense = models.Expense(
        payer_id=current_user.id,
        group_id=group.id,
        total=expense_in.total,
        description=expense_in.description
    )
    db.add(expense)
    db.flush()

    # Add shares for others only
    for member in members:
        if member.id == current_user.id:
            continue  # Payer doesn't owe anything
        share = models.Share(
            expense_id=expense.id,
            user_id=member.id,
            amount=per_person_share,
            paid=False
        )
        db.add(share)

    db.commit()
    db.refresh(expense)
    return expense

def get_user_balances(db: Session, current_user: models.User, approved_only: bool = False):
    # 1. Shares where current_user is debtor (owes money)
    owed_by_query = (
        db.query(models.Share)
          .join(models.Expense)
          .filter(models.Share.user_id == current_user.id, models.Share.paid == False)
    )
    if approved_only:
        owed_by_query = owed_by_query.filter(models.Expense.status == "approved")
    owed_by = owed_by_query.all()

    # 2. Shares where current_user is payer (others owe them)
    owed_to_query = (
        db.query(models.Share)
          .join(models.Expense)
          .filter(models.Expense.payer_id == current_user.id, models.Share.paid == False)
    )
    if approved_only:
        owed_to_query = owed_to_query.filter(models.Expense.status == "approved")
    owed_to = owed_to_query.all()

    balances: dict[models.User, Decimal] = defaultdict(Decimal)

    # For each share current_user owes, subtract from balance of payer
    for share in owed_by:
        payer = share.expense.payer
        balances[payer] -= share.amount

    # For each share others owe current_user, add to their balance
    for share in owed_to:
        debtor = share.user
        balances[debtor] += share.amount

    # Build output list
    result = []
    for user, amt in balances.items():
        # Skip zero balances for cleanliness
        if amt == 0:
            continue
        result.append({
            "user_id": user.id,
            "email": user.email,
            "amount": amt
        })

    return result

def settle_debt(
    db: Session,
    current_user: models.User,
    target_user_id: int,
    payment: Decimal
) -> list[models.Share]:
    """
    current_user pays 'payment' amount to target_user (the payer).
    This will:
      - find all unpaid shares where share.user_id == current_user.id
        AND expense.payer_id == target_user_id
      - apply payment sequentially (oldest first)
      - reduce share.amount by paid portion
      - mark share.paid=True when share.amount <= 0
    Returns list of updated Share objects.
    """
    # 1. Query unpaid shares you owe to that user, oldest first
    shares = (
        db.query(models.Share)
          .join(models.Expense)
          .filter(
              models.Share.user_id == current_user.id,
              models.Share.paid == False,
              models.Expense.payer_id == target_user_id
          )
          .order_by(models.Expense.created_at)
          .all()
    )

    if not shares:
        raise ValueError("No unpaid debts to that user")

    remaining_payment = payment
    updated = []

    for share in shares:
        if remaining_payment <= 0:
            break

        to_pay = min(share.amount, remaining_payment)
        share.amount -= to_pay
        remaining_payment -= to_pay

        if share.amount <= Decimal("0.00"):
            share.paid = True
            share.amount = Decimal("0.00")

        updated.append((share.id, to_pay, share.amount))
        db.add(share)

    if remaining_payment > 0:
        # you paid more than you owed
        raise ValueError(f"Overpayment by {remaining_payment}")

    db.commit()
    # Refresh and return updated shares
    result = []
    for share_id, paid_amt, rem_amt in updated:
        sh = db.query(models.Share).get(share_id)
        result.append(sh)
    return result